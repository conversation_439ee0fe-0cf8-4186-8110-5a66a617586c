import logging
import uuid
import json
import os
import time
import tempfile
import paramiko
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, List
from utils.resource_manager import ResourceManager
import environ

# 加载环境变量
env = environ.Env()
# 读取.env文件
BASE_DIR = Path(__file__).resolve().parent.parent
environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class YOLOv8DockerTrainer:
    """
    YOLOv8训练器类 - 基于资源调度平台的容器化训练解决方案
    
    特性：
    - 支持NPU和GPU训练环境
    - 实时状态监控和日志获取
    
    前端配置文件格式示例:
    {
        "id": "task_001",                # 任务ID
        "algorithm": {
            "version": "v8",             # 算法版本
            "modelPath": "yolov8n.pt"    # 模型路径
        },
        "training": {
            "dataset": {
                "id": 1,                 # 数据集ID
                "name": "coco"           # 数据集名称
            },
            "validationRatio": 0.2       # 验证集比例
        },
        "parameters": {
            "epochs": 100,               # 训练轮数
            "batchSize": 16,             # 批次大小
            "learningRate": 0.01,        # 学习率
            "learningRateStrategy": "余弦衰减",  # 学习率策略
            "maxGradNorm": 10.0,         # 最大梯度范数
            "computeType": "float32"     # 计算类型 (float32/bf16)
        },
        "otherParams": {
            "optimizer": "SGD",          # 优化器类型 (SGD/Adam/AdamW)
            "momentum": 0.937,           # 动量
            "weightDecay": 0.0005,       # 权重衰减
            "earlyStopping": 10,         # 早停轮数
            "checkpointFreq": 10,        # 检查点保存频率
            "warmupSteps": 1000,         # 预热步数
            "labelSmoothing": 0.1,       # 标签平滑
            "dropout": 0.2,              # Dropout比例
            "useMixedPrecision": true,   # 是否使用混合精度训练
            "useGradientClipping": true  # 是否使用梯度裁剪
        },
        "resources": {
            "cpu": 4,                    # CPU核心数
            "npu": 1,                    # NPU数量
            "storage": 100,              # 存储大小(GB)
            "specification_id": null,     # 可选，指定产品ID
            "mirror_image": null,        # 可选，指定镜像名称
            "storage_id": null,          # 可选，指定存储ID
            "structure": "amd64"         # 系统架构(amd64/arm64)
        }
    }
    
    注意事项:
    1. 所有数值类型参数都应该以字符串形式传递
    2. 资源配置中的specification_id, mirror_image, storage_id 是可选的
    3. 如果不指定具体的资源ID，系统会自动选择合适的资源
    4. 数据集路径应该遵循 datasets/{dataset_name}/data.yaml 的格式
    5. 模型路径可以是预训练模型名称或本地路径
    6. 容器启动时会自动执行训练任务，无需SSH连接
    7. 支持实时状态监控和日志查看
    """
    
    def __init__(self, training_task):
        """
        初始化训练器
        参数:
            training_task: TrainingTask模型实例，包含以下主要字段：
                - algorithm_version: 算法版本 (v8)
                - model_path: 模型路径
                - dataset_id: 数据集ID
                - dataset_name: 数据集名称
                - cpu_count: CPU数量
                - npu_count: NPU数量
                - storage_size: 存储大小
                - learning_rate: 学习率
                - epochs: 训练轮数
                - batch_size: 批处理大小
                - optimizer: 优化器
                - momentum: 动量
                - weight_decay: 权重衰减
                等...
        """
        self.training_task = training_task
        self.task_info = None
        
        # 通过资源调度平台请求资源
        self.resource_manager = ResourceManager()
        
        # 训练脚本和配置文件的本地路径
        self.train_script_path = os.path.join(os.path.dirname(__file__), 'train_yolov8_multi.py')
        self.ssh_client = None
        self.remote_dir = "/workspace"
        
        # 移除缓存机制，确保始终获取最新的训练指标

    def _get_remote_script_path(self, script_name):
        """
        获取远程脚本路径
        Args:
            script_name: 脚本名称
        Returns:
            str: 远程脚本路径
        """
        return f"{self.remote_dir}/{script_name}"

    def _find_product_by_id(self, products: list, specification_id: str) -> Optional[dict]:
        """根据规格ID查找产品"""
        for product in products:
            if product.get('specificationId') == specification_id:
                return product
        return None

    def _find_image_by_name(self, images: list, image_name: str) -> Optional[str]:
        """根据镜像名称查找镜像"""
        for image in images:
            if image == image_name:
                return image
        return None

    def _request_resource(self) -> bool:
        """请求训练资源"""
        try:
            # 1. 查询可用产品
            products = self.resource_manager.query_products()
            if not products:
                logger.error("没有可用的计算资源")
                return False

            # 获取配置参数
            resources = {
                'cpu': int(self.training_task.cpu_count),
                'npu': int(self.training_task.npu_count),
                'specification_id': getattr(self.training_task, 'specification_id', None),
                'mirror_image': getattr(self.training_task, 'mirror_image', None),
                'structure': getattr(self.training_task, 'structure', 'amd64')
            }

            # 选择产品
            selected_product = None
            if resources.get('specification_id'):
                selected_product = self._find_product_by_id(products, resources['specification_id'])
                if not selected_product:
                    logger.error(f"未找到指定的产品ID: {resources['specification_id']}")
                    return False
            else:
                # 获取第一个可用产品
                for item in products:
                    if item.get('hoseAlias') == 'node01-910B3':
                        continue
                    else:
                        selected_product = item
                        break

            # 2. 查询镜像信息
            images = self.resource_manager.query_images(selected_product.get('structure', 'amd64'))
            if not images:
                logger.error("没有可用的镜像")
                return False

            selected_image = None
            if resources.get('mirror_image'):
                selected_image = self._find_image_by_name(images, resources['mirror_image'])
                if not selected_image:
                    logger.error(f"未找到指定的镜像: {resources['mirror_image']}")
                    return False
            else:
                # 选择包含深度学习环境的镜像
                for image in images:
                    if any(keyword in image.lower() for keyword in ['pytorch', 'ultralytics', 'yolov8:', 'torch']):
                        selected_image = image
                        break
                if not selected_image:
                    selected_image = images[0]

            # 3. 创建任务
            taskname = f"yolov8训练任务-{uuid.uuid4().hex[:8]}"
            
            # 使用NPU数量作为specification_amount参数
            specification_amount = int(resources.get('npu', 1))  # 默认为1
            
            task = self.resource_manager.create_task(
                calculate_device_id=selected_product['calculateDeviceId'],
                specification_id=selected_product['specificationId'],
                mirror_image=selected_image,
                task_name=taskname,
                gpu_mode=selected_product['gpuMode'],
                specification_amount=specification_amount  # 传递NPU数量
            )

            if task.get("code") != "0":
                logger.error("任务创建失败")
                return False

            # 4. 查询任务状态获取详细信息
            task_info = self.resource_manager.query_task(taskname)
            if not task_info or not task_info.get('records'):
                logger.error("获取任务信息失败")
                return False

            self.task_info = {
                'taskName': taskname,
                'taskId': task_info['records'][0]['id'],
                'ip': task_info['records'][0]['agentIp'],
                'port': task_info['records'][0]['sshPort'],
                'password': task_info['records'][0]['sshPasswd'],
                'status': task_info['records'][0]['status'],
            }

            logger.info(f"成功创建训练任务，任务ID: {task_info['records'][0]['id']}")
            return True

        except Exception as e:
            logger.error(f"请求资源失败: {e}")
            return False

    def _connect_ssh(self):
        """连接到远程服务器"""
        if not self.task_info:
            logger.error("没有任务信息，无法连接SSH")
            return False

        try:
            # 创建SSH客户端
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接远程服务器
            logger.info(f"正在连接服务器: {self.task_info['ip']}:{self.task_info['port']}")
            self.ssh_client.connect(
                hostname=self.task_info['ip'],
                port=int(self.task_info['port']),
                username='root',
                password=self.task_info['password'],
                timeout=30
            )
            logger.info("SSH连接成功")
            return True
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            return False

    def _upload_training_files(self):
        """上传训练所需的文件"""
        if not self.ssh_client:
            logger.error("SSH未连接，无法上传文件")
            return False

        temp_path = None
        try:
            # 确保远程工作目录存在
            mkdir_cmd = f"mkdir -p {self.remote_dir}"
            self.ssh_client.exec_command(mkdir_cmd)
            
            # 创建SFTP客户端
            sftp = self.ssh_client.open_sftp()
            
            # 上传训练脚本
            logger.info(f"上传训练脚本: {self.train_script_path}")
            # 单机单卡训练脚本
            #remote_script_path = f"{self.remote_dir}/train_yolov8.py"
            # 单机多卡/单卡 训练脚本
            remote_script_path = f"{self.remote_dir}/train_yolov8_multi.py"
            sftp.put(self.train_script_path, remote_script_path)
            
            # 创建并上传训练配置文件
            config = self._generate_training_config()
            print("配置文件：", config)
            
            # 使用tempfile模块创建临时文件
            fd, temp_path = tempfile.mkstemp(suffix='.json', prefix='training_config_')
            try:
                with os.fdopen(fd, 'w') as temp:
                    json.dump(config, temp, indent=2)
                
                logger.info(f"创建临时配置文件: {temp_path}")
                
                # 上传临时文件
                remote_config_path = f"{self.remote_dir}/training_config.json"
                sftp.put(temp_path, remote_config_path)
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    logger.info(f"已删除临时文件: {temp_path}")
            
            # 关闭SFTP连接
            sftp.close()
            
            logger.info("训练文件上传成功")
            return True
        except Exception as e:
            logger.error(f"上传文件失败: {e}")
            # 确保清理临时文件
            if temp_path and os.path.exists(temp_path):
                os.remove(temp_path)
                logger.info(f"异常处理中删除临时文件: {temp_path}")
            return False

    def _execute_training_script(self):
        """执行远程训练脚本"""
        if not self.ssh_client:
            logger.error("SSH未连接，无法执行训练脚本")
            return False

        try:
            # 1. 确保远程工作目录存在
            logger.info(f"创建远程工作目录: {self.remote_dir}")
            mkdir_cmd = f"mkdir -p {self.remote_dir}"
            self.ssh_client.exec_command(mkdir_cmd)
            
            # 2. 准备训练目录
            logger.info("准备训练目录...")
            prep_dirs_cmd = f"mkdir -p {self.remote_dir}/runs_detect"
            self.ssh_client.exec_command(prep_dirs_cmd)
            
            # 3. 通过query_bucket_list获取挂载信息
            logger.info("获取存储挂载信息...")
            
            # 使用已创建资源的任务信息查询详细信息
            task_info = self.resource_manager.query_task(self.task_info['taskName'])
            if not task_info or not task_info.get('records'):
                logger.error("无法获取任务信息")
                return False
            
            # 从任务信息中获取calculateDeviceId
            calculate_device_id = task_info['records'][0].get('calculateDeviceId')
            if not calculate_device_id:
                logger.error("无法获取calculateDeviceId")
                return False
            
            # 查询存储挂载信息
            buckets = self.resource_manager.query_bucket_list(calculate_device_id)
            if not buckets:
                logger.error("无法获取存储挂载信息")
                return False
                
            # 4. 查找挂载路径
            user_data_mount_path = None

            for bucket in buckets:
                mount_path = bucket.get('mountPath', '')
                # 查找用户数据挂载路径
                if "siton-data-" in mount_path:
                    user_data_mount_path = mount_path
                    break

            # 如果没找到特定挂载点，使用默认路径（admin网盘路径）
            if not user_data_mount_path:
                user_data_mount_path = env('SITON_DATA_MOUNT_PATH', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9')

            # 获取ULTRALYTICS_DIR用于--mount_path参数
            ultralytics_mount_path = env('ULTRALYTICS_DIR', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9/data/ultralytics_v8')
            
            # 构建模型和数据集目录
            # 对于特定的挂载路径，添加models和datasets子目录
            if "siton-data-" in user_data_mount_path:
                model_mount_path = f"{user_data_mount_path}/models"
                dataset_mount_path = f"{user_data_mount_path}/datasets"
            else:
                # 对其他路径使用之前的逻辑
                model_mount_path = user_data_mount_path
                dataset_mount_path = user_data_mount_path
            
            if not model_mount_path:
                model_mount_path = "/root/siton-data/models"  # 默认模型路径
            
            # 拼接最终的数据集和模型路径
            dataset_path = f"{dataset_mount_path}/{self.training_task.dataset_name}/data.yaml"
            model_path = f"{model_mount_path}/{self.training_task.model_path}"
            
            # 5. 生成任务ID并构建命令
            task_id = f'task_{self.training_task.id}'
            
            # 构建命令行，传递模型和数据集路径参数
            # 检查远程服务器的NPU卡数量
            # 设置环境变量命令
            set_env_cmd = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH'
            
            # 检查NPU数量的Python命令
            python_cmd = 'import torch_npu; print(torch_npu.npu.device_count() if torch_npu.npu.is_available() else 0)'
            
            # 组合完整命令，使用双引号包裹Python命令
            check_npu_cmd = f'bash -c \'{set_env_cmd} && python -c "{python_cmd}"\''
            
            # 执行命令并获取结果
            stdin, stdout, stderr = self.ssh_client.exec_command(check_npu_cmd)
            npu_count = int(stdout.read().decode().strip() or 0)
            logger.info(f"远程服务器检测到 {npu_count} 个NPU设备")

            # 根据NPU数量构建命令
            if npu_count > 1:
                # 使用torch.distributed.run启动分布式训练
                # --nproc_per_node: 指定每个节点上启动的进程数，这里设为NPU数量，每个NPU对应一个训练进程
                # 这样可以实现数据并行训练，提高训练效率
                command = f"cd {self.remote_dir} && python -m torch.distributed.run --nproc_per_node={npu_count} train_yolov8_multi.py --model {model_path} --data {dataset_path} --task_id {task_id} --config training_config.json --mount_path {ultralytics_mount_path}"
            else:
                command = f"cd {self.remote_dir} && python train_yolov8_multi.py --model {model_path} --data {dataset_path} --task_id {task_id} --config training_config.json --mount_path {ultralytics_mount_path}"
            
            logger.info(f"执行训练命令: {command}")
            logger.info(f"数据集路径: {dataset_path}")
            logger.info(f"模型路径: {model_path}")
            
            # 使用nohup后台运行训练脚本，防止SSH断开导致训练中断
            run_cmd = f"nohup bash -c '{set_env_cmd} && {command}' > {self.remote_dir}/train.log 2>&1 &"
            stdin, stdout, stderr = self.ssh_client.exec_command(run_cmd)
            
            # 检查是否有错误
            err = stderr.read().decode().strip()
            if err:
                logger.error(f"执行训练命令出错: {err}")
                return False
            
            logger.info("训练脚本已在后台启动")
            return True
        except Exception as e:
            logger.error(f"执行训练脚本失败: {e}")
            return False

    def _generate_training_config(self) -> dict:
        """生成训练配置"""
        return {
            'algorithm': {
                'version': getattr(self.training_task, 'algorithm_version', 'v8'),
                'modelPath': getattr(self.training_task, 'model_path', 'yolov8n.pt')
            },
            'training': {
                'dataset': {
                    'name': getattr(self.training_task, 'dataset_name', ''),
                    'path': f"/workspace/datasets/{getattr(self.training_task, 'dataset_name', '')}/data.yaml"
                },
                'validationRatio': float(getattr(self.training_task, 'validation_ratio', 0.2))
            },
            'parameters': {
                'learningRate': getattr(self.training_task, 'learning_rate', 0.01),
                'epochs': getattr(self.training_task, 'epochs', 100),
                'batchSize': getattr(self.training_task, 'batch_size', 16),
                'learningRateStrategy': getattr(self.training_task, 'learning_rate_strategy', '余弦衰减'),
                'computeType': getattr(self.training_task, 'compute_type', 'float32')
            },
            'otherParams': {
                'optimizer': getattr(self.training_task, 'optimizer', 'SGD'),
                'momentum': getattr(self.training_task, 'momentum', 0.937),
                'weightDecay': getattr(self.training_task, 'weight_decay', 0.0005),
                'useGradientClipping': getattr(self.training_task, 'use_gradient_clipping', True),
                'useMixedPrecision': getattr(self.training_task, 'use_mixed_precision', False),
                'checkpointFreq': getattr(self.training_task, 'checkpoint_freq', 1)
            },
            'resources': {
                'cpuCount': getattr(self.training_task, 'cpu_count', 4),
                'npuCount': getattr(self.training_task, 'npu_count', 1)
            }
        }

    def get_training_metrics(self, max_retries=3) -> Dict:
        """
        从远程服务器获取训练指标数据
        Args:
            max_retries: 最大重试次数
        返回:
            dict: 包含训练指标的字典，如果获取失败则返回空字典
        """
        logger.info("获取最新训练指标数据")

        # 如果没有任务信息或SSH连接，返回空字典
        if not self.task_info or not self.ssh_client:
            logger.warning("无法获取训练指标：任务未启动或SSH未连接")
            return {}

        # 重试机制
        for attempt in range(max_retries):
            try:
                logger.info(f"第{attempt+1}次尝试获取训练指标数据")

                # 检查metrics文件是否存在
                check_cmd = f"test -f {self.remote_dir}/training_metrics.json && echo 'exists' || echo 'not exists'"
                stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
                result = stdout.read().decode().strip()

                if result != 'exists':
                    logger.warning(f"第{attempt+1}次尝试：训练指标文件不存在")
                    if attempt < max_retries - 1:
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    return {}

                # 读取训练指标文件
                read_cmd = f"cat {self.remote_dir}/training_metrics.json"
                _, stdout, _ = self.ssh_client.exec_command(read_cmd)

                # 解析JSON数据
                metrics_json = stdout.read().decode()
                if not metrics_json:
                    logger.warning(f"第{attempt+1}次尝试：训练指标文件为空")
                    if attempt < max_retries - 1:
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    return {}

                # 处理可能包含多行JSON的情况
                metrics_lines = metrics_json.strip().split('\n')
                metrics_list = []

                for line in metrics_lines:
                    if line.strip():
                        try:
                            metric_data = json.loads(line)
                            metrics_list.append(metric_data)
                        except json.JSONDecodeError:
                            logger.warning(f"无法解析训练指标行: {line}")

                if not metrics_list:
                    logger.warning(f"第{attempt+1}次尝试：未找到有效的训练指标数据")
                    if attempt < max_retries - 1:
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    return {}

                # 提取训练指标并组织成合适的格式
                epochs = []
                train_losses = []
                val_losses = []
                timestamps = []
                cpu_usages = []
                gpu_usages = []
                npu_usages = []
                memory_usages = []

                for metric in metrics_list:
                    # 收集训练指标
                    epochs.append(metric.get('epoch', 0))

                    # 损失指标
                    train_box_loss = metric.get('train/box_loss', 0.0)
                    train_cls_loss = metric.get('train/cls_loss', 0.0)
                    train_dfl_loss = metric.get('train/dfl_loss', 0.0)
                    # 计算总训练损失
                    total_train_loss = train_box_loss + train_cls_loss + train_dfl_loss
                    train_losses.append(total_train_loss)

                    # 使用mAP作为验证损失（实际YOLOv8不直接提供val_loss）
                    val_losses.append(1.0 - metric.get('metrics/mAP50-95', 0.0))

                    # 资源使用指标
                    cpu_usages.append(metric.get('cpu_usage', 0.0))
                    gpu_usages.append(metric.get('gpu_usage', 0.0))
                    npu_usages.append(metric.get('npu_usage', 0.0))
                    memory_usages.append(metric.get('memory_usage', 0.0))

                    # 时间戳 - 确保格式一致
                    timestamp = metric.get('timestamp', time.time())
                    if isinstance(timestamp, (int, float)):
                        # 如果是时间戳数字，转换为datetime对象
                        timestamp = datetime.fromtimestamp(timestamp)
                    timestamps.append(timestamp)

                # 构建返回的指标数据
                metrics_data = {
                    'epochs': epochs,
                    'train_losses': train_losses,
                    'val_losses': val_losses,
                    'timestamps': timestamps,
                    'cpu_usages': cpu_usages,
                    'gpu_usages': gpu_usages,
                    'npu_usages': npu_usages,
                    'memory_usages': memory_usages,
                    'precision': metrics_list[-1].get('metrics/precision', 0.0),
                    'recall': metrics_list[-1].get('metrics/recall', 0.0),
                    'mAP50': metrics_list[-1].get('metrics/mAP50', 0.0),
                    'mAP50-95': metrics_list[-1].get('metrics/mAP50-95', 0.0)
                }

                logger.info(f"第{attempt+1}次尝试成功获取训练指标，包含 {len(epochs)} 个epoch")

                return metrics_data

            except Exception as parse_error:
                logger.error(f"第{attempt+1}次尝试解析训练指标数据时发生错误: {parse_error}")
                if attempt < max_retries - 1:
                    time.sleep(1)  # 等待1秒后重试
                    continue
                return {}

        # 所有重试都失败了
        logger.error(f"经过 {max_retries} 次重试，仍无法获取训练指标")
        return {}
    


    def get_task_status(self) -> Dict:
        """获取资源调度平台创建任务状态"""
        try:
            if not self.task_info:
                return {'status': 'not_started'}

            # 首先检查数据库中的状态，如果是暂停相关状态，优先返回数据库状态
            if hasattr(self, 'training_task') and self.training_task:
                # 刷新数据库状态，确保获取最新状态
                self.training_task.refresh_from_db()
                db_status = self.training_task.status
                if db_status in ['paused', 'pausing', 'resuming', 'cancelling', 'cancelled']:
                    logger.info(f"数据库状态为 {db_status}，优先返回数据库状态而不是资源平台状态")
                    return {'status': db_status}

            # 通过资源调度平台API查询任务状态
            task_info = self.resource_manager.query_task(self.task_info['taskName'])
            if not task_info or not task_info.get('records'):
                return {'status': 'unknown', 'error': '无法获取任务信息'}

            task_status = task_info['records'][0].get('status')

            status_mapping = {
                1: 'downloading',  # 镜像下载中
                2: 'packaging',    # 镜像打包中
                3: 'creating',     # 创建中
                4: 'running',      # 运行中
                5: 'shutting_down', # 关机中
                6: 'shutdown',     # 已关机
                7: 'unavailable',  # 不可用
                8: 'releasing',    # 释放中
                9: 'no_card_mode', # 无卡模式
                10: 'starting',    # 正在开机
                11: 'restarting',  # 重启中
                12: 'starting_no_card', # 无卡模式启动中
                13: 'resetting',   # 重置系统中
                14: 'upgrading'    # 升配中
            }

            # 获取资源平台状态
            status_str = status_mapping.get(task_status, 'unknown')

            # 如果任务正在运行，尝试检查训练是否完成
            if status_str == 'running' and self.ssh_client:
                try:
                    # 获取训练配置的总轮数
                    total_epochs = self.training_task.epochs

                    # 首先检查训练进程是否还在运行
                    process_cmd = "ps aux | grep train_yolov8_multi.py | grep -v grep"
                    _, stdout, _ = self.ssh_client.exec_command(process_cmd)
                    processes = stdout.read().decode().strip()

                    # 检查训练指标文件是否存在
                    check_cmd = f"test -f {self.remote_dir}/training_metrics.json && echo 'exists' || echo 'not exists'"
                    _, stdout, _ = self.ssh_client.exec_command(check_cmd)
                    result = stdout.read().decode().strip()

                    if result == 'exists':
                        # 读取训练指标文件
                        read_cmd = f"cat {self.remote_dir}/training_metrics.json"
                        _, stdout, _ = self.ssh_client.exec_command(read_cmd)
                        metrics_json = stdout.read().decode()

                        # 解析JSON数据
                        if metrics_json:
                            metrics_lines = metrics_json.strip().split('\n')
                            if metrics_lines:
                                # 获取最后一行的指标数据
                                try:
                                    last_metric = json.loads(metrics_lines[-1])
                                    current_epoch = last_metric.get('epoch', 0)

                                    # 确保类型转换正确
                                    current_epoch = int(current_epoch)  # 确保是整数
                                    total_epochs_int = int(total_epochs)  # 确保是整数

                                    # 检查是否已完成所有训练轮数
                                    if current_epoch >= total_epochs_int - 1:  # epoch从0开始计数
                                        logger.info(f"训练已完成! 当前epoch: {current_epoch}, 总epochs: {total_epochs_int}")
                                        status_str = 'completed'
                                        # 如果训练进程已经结束，确认为完成状态
                                        if not processes:
                                            logger.info("训练进程已结束，确认训练完成")
                                        else:
                                            logger.info("训练轮数已达到，但进程仍在运行，等待进程结束")
                                            # 等待一段时间让进程自然结束
                                            time.sleep(5)

                                            # 再次检查进程状态
                                            _, stdout, _ = self.ssh_client.exec_command(process_cmd)
                                            processes_after = stdout.read().decode().strip()

                                            if not processes_after:
                                                logger.info("训练进程已结束，确认训练完成")
                                                status_str = 'completed'
                                            else:
                                                logger.warning("训练进程仍在运行，保持running状态")

                                        # 如果确认为完成状态，执行完成后的处理
                                        if status_str == 'completed':
                                            # 等待一小段时间确保文件完全写入
                                            time.sleep(2)

                                            # 多次尝试获取最终指标，确保数据完整
                                            final_metrics = None
                                            for attempt in range(3):
                                                final_metrics = self.get_training_metrics()
                                                if final_metrics and final_metrics.get('epochs'):
                                                    logger.info(f"第{attempt+1}次尝试获取最终指标成功，包含{len(final_metrics.get('epochs', []))}个epoch")
                                                    break
                                                else:
                                                    logger.warning(f"第{attempt+1}次尝试获取最终指标失败，等待2秒后重试")
                                                    time.sleep(2)

                                            # 异步保存训练模型信息到数据库（避免阻塞SSE）
                                            logger.info("训练完成，启动异步任务保存模型信息到数据库")
                                            self.schedule_model_save_task()
                                    else:
                                        logger.debug(f"训练进行中: epoch {current_epoch}/{total_epochs_int-1}")

                                        # 如果进程不存在但训练未完成，可能是异常情况
                                        if not processes:
                                            logger.warning(f"训练进程已结束但训练未完成 (epoch {current_epoch}/{total_epochs_int-1})，可能训练失败")
                                            status_str = 'failed'

                                        # 等待一小段时间确保文件完全写入
                                        time.sleep(2)
                                        # 多次尝试获取最终指标，确保数据完整（用于验证）
                                        final_metrics = None
                                        for attempt in range(3):
                                            final_metrics = self.get_training_metrics()
                                            if final_metrics and final_metrics.get('epochs'):
                                                logger.info(f"第{attempt+1}次尝试获取最终指标成功，包含{len(final_metrics.get('epochs', []))}个epoch")
                                                break
                                            else:
                                                logger.warning(f"第{attempt+1}次尝试获取最终指标失败，等待2秒后重试")
                                                time.sleep(2)
                                        # 获取并保存训练模型信息到数据库
                                        logger.info("训练完成，获取并保存模型信息到数据库")
                                        self.save_training_models_to_db()
                                except Exception as e:
                                    logger.error(f"解析训练指标失败: {e}")
                except Exception as e:
                    logger.error(f"检查训练完成状态时出错: {e}")

            return {
                'status': status_str,
                'task_id': self.task_info['taskId'],
                'ip': self.task_info['ip'],
                'port': self.task_info['port']
            }

        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return {'status': 'unknown', 'error': str(e)}

       

    def get_training_logs(self, lines=100, mode='tail'):
        """
        获取训练日志
        Args:
            lines: 获取的行数
            mode: 获取模式 ('tail': 最后几行, 'head': 前几行, 'all': 全部)
        Returns:
            str: 日志内容
        """
        if not self.task_info or not self.ssh_client:
            return "任务未启动或SSH未连接"

        try:
            # 确保工作目录存在
            mkdir_cmd = f"mkdir -p {self.remote_dir}"
            self.ssh_client.exec_command(mkdir_cmd)

            # 检查日志文件是否存在
            check_cmd = f"test -f {self.remote_dir}/train.log && echo 'exists' || echo 'not exists'"
            _, stdout, _ = self.ssh_client.exec_command(check_cmd)
            result = stdout.read().decode().strip()

            if result != 'exists':
                return "训练日志文件不存在，可能训练尚未开始或日志未生成"

            # 根据模式选择命令
            if mode == 'all':
                command = f"cat {self.remote_dir}/train.log"
            elif mode == 'head':
                command = f"head -n {lines} {self.remote_dir}/train.log"
            else:  # tail (默认)
                command = f"tail -n {lines} {self.remote_dir}/train.log"

            _, stdout, _ = self.ssh_client.exec_command(command)

            # 读取输出
            logs = stdout.read().decode()
            return logs if logs else "暂无日志"
        except Exception as e:
            logger.error(f"获取训练日志失败: {e}")
            return f"获取日志失败: {str(e)}"


    def search_logs(self, pattern, lines=None):
        """
        在日志中搜索特定模式
        Args:
            pattern: 搜索模式
            lines: 限制搜索的行数 (None表示搜索全部)
        Returns:
            str: 匹配的日志行
        """
        if not self.task_info or not self.ssh_client:
            return "任务未启动或SSH未连接"

        try:
            log_file_path = f"{self.remote_dir}/train.log"

            # 检查文件是否存在
            check_cmd = f"test -f {log_file_path} && echo 'exists' || echo 'not exists'"
            _, stdout, _ = self.ssh_client.exec_command(check_cmd)
            result = stdout.read().decode().strip()

            if result != 'exists':
                return "日志文件不存在"

            # 构建搜索命令
            if lines:
                command = f"tail -n {lines} {log_file_path} | grep -n '{pattern}'"
            else:
                command = f"grep -n '{pattern}' {log_file_path}"

            _, stdout, _ = self.ssh_client.exec_command(command)

            # 读取搜索结果
            results = stdout.read().decode()
            return results if results else f"未找到匹配 '{pattern}' 的日志"

        except Exception as e:
            logger.error(f"搜索日志失败: {e}")
            return f"搜索日志失败: {str(e)}"

    def start(self) -> bool:
        """启动训练任务"""
        try:
            # 1. 请求资源创建任务
            if not self._request_resource():
                return False

            # 2. 等待一段时间，确保服务器启动完成
            logger.info("等待服务器启动...")
            time.sleep(10)

            # 3. 建立SSH连接
            if not self._connect_ssh():
                return False

            # 4. 清理旧的缓存文件（训练开始前）
            logger.info("训练开始前清理旧的数据集缓存文件...")
            self._cleanup_dataset_cache()

            # 5. 上传训练文件
            if not self._upload_training_files():
                return False

            # 6. 执行训练脚本
            if not self._execute_training_script():
                return False

            logger.info("训练任务已成功启动")
            return True

        except Exception as e:
            logger.error(f"启动训练任务失败: {e}")
            return False

    def _cleanup_dataset_cache(self):
        """清理数据集缓存文件"""
        if not self.ssh_client:
            logger.warning("SSH未连接，无法清理缓存文件")
            return False

        try:
            # 获取数据集挂载路径
            user_data_mount_path = env('SITON_DATA_MOUNT_PATH', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9')

            # 尝试从任务信息获取实际挂载路径
            if self.task_info:
                try:
                    task_info = self.resource_manager.query_task(self.task_info['taskName'])
                    if task_info and task_info.get('records'):
                        calculate_device_id = task_info['records'][0].get('calculateDeviceId')
                        if calculate_device_id:
                            buckets = self.resource_manager.query_bucket_list(calculate_device_id)
                            for bucket in buckets:
                                mount_path = bucket.get('mountPath', '')
                                if "siton-data-" in mount_path:
                                    user_data_mount_path = mount_path
                                    break
                except Exception as e:
                    logger.warning(f"获取挂载路径失败，使用默认路径: {e}")

            # 构建数据集路径
            if "siton-data-" in user_data_mount_path:
                dataset_mount_path = f"{user_data_mount_path}/datasets"
            else:
                dataset_mount_path = user_data_mount_path

            dataset_labels_path = f"{dataset_mount_path}/{self.training_task.dataset_name}/labels"

            # 检查labels目录是否存在
            check_cmd = f"test -d {dataset_labels_path} && echo 'exists' || echo 'not exists'"
            _, stdout, _ = self.ssh_client.exec_command(check_cmd)
            result = stdout.read().decode().strip()

            if result == 'exists':
                # 删除labels目录下的所有.cache文件
                cleanup_cmd = f"find {dataset_labels_path} -name '*.cache' -type f -delete"
                _, _, stderr = self.ssh_client.exec_command(cleanup_cmd)

                # 检查是否有错误
                err = stderr.read().decode().strip()
                if err:
                    logger.warning(f"清理缓存文件时出现警告: {err}")
                else:
                    logger.info(f"已清理数据集缓存文件: {dataset_labels_path}/*.cache")

                # 验证清理结果
                verify_cmd = f"find {dataset_labels_path} -name '*.cache' -type f | wc -l"
                _, stdout, _ = self.ssh_client.exec_command(verify_cmd)
                remaining_files = stdout.read().decode().strip()
                logger.info(f"清理后剩余.cache文件数量: {remaining_files}")
                return True
            else:
                logger.info(f"数据集labels目录不存在: {dataset_labels_path}")
                return True

        except Exception as e:
            logger.error(f"清理数据集缓存文件失败: {e}")
            return False

    def stop(self) -> bool:
        """停止训练任务"""
        try:
            if not self.task_info:
                logger.warning("没有运行中的任务")
                return True

            # 先通过SSH停止训练进程
            if self.ssh_client:
                try:
                    self.ssh_client.exec_command("pkill -f train_yolov8.py")
                    logger.info("已发送停止训练进程命令")
                    # 关闭SSH连接
                    self.ssh_client.close()
                    self.ssh_client = None
                except:
                    logger.warning("停止训练进程失败，继续关闭任务")

            # 通过资源调度平台停止任务
            success = self.resource_manager.stop_task(self.task_info['taskId'])
            if success.get("code") == "0":
                logger.info(f"任务 {self.task_info['taskId']} 已停止")
                # 删除任务
                time.sleep(5)  # 等待5秒，确保任务已经完全停止
                status = self.resource_manager.delete_task(self.task_info['taskId'])
                if status.get("code") == "0":
                    logger.info(f"任务 {self.task_info['taskId']} 已删除")
                    return True
                else:
                    logger.info(f"任务 {self.task_info['taskId']} 删除失败")
                    return False
            else:
                logger.error(f"停止任务 {self.task_info['taskId']} 失败")
                return False

        except Exception as e:
            logger.error(f"停止训练任务失败: {e}")
            return False

    def pause(self) -> bool:
        """暂停训练任务（通过SSH杀掉训练进程）"""
        try:
            if not self.task_info:
                logger.warning("没有运行中的任务")
                return True

            # 通过SSH连接到服务器并杀掉训练进程
            if self.ssh_client:
                try:
                    # 查找并杀掉训练进程
                    # 首先查找进程
                    stdin, stdout, stderr = self.ssh_client.exec_command("ps aux | grep train_yolov8_multi.py | grep -v grep")
                    processes = stdout.read().decode().strip()

                    if processes:
                        logger.info(f"找到训练进程: {processes}")

                        # 杀掉训练进程（使用SIGTERM信号，允许进程优雅退出）
                        stdin, stdout, stderr = self.ssh_client.exec_command("pkill -f train_yolov8_multi.py")

                        # 等待命令执行完成
                        exit_status = stdout.channel.recv_exit_status()

                        if exit_status == 0:
                            logger.info("训练进程已成功暂停")
                            return True
                        else:
                            # 如果SIGTERM失败，尝试使用SIGKILL强制杀掉
                            logger.warning("优雅停止失败，尝试强制停止")
                            stdin, stdout, stderr = self.ssh_client.exec_command("pkill -9 -f train_yolov8_multi.py")
                            exit_status = stdout.channel.recv_exit_status()

                            if exit_status == 0:
                                logger.info("训练进程已强制停止")
                                return True
                            else:
                                logger.error("无法停止训练进程")
                                return False
                    else:
                        logger.info("未找到运行中的训练进程")
                        return True

                except Exception as ssh_error:
                    logger.error(f"SSH执行暂停命令失败: {ssh_error}")
                    return False
            else:
                logger.error("SSH连接不可用")
                return False

        except Exception as e:
            logger.error(f"暂停训练任务失败: {e}")
            return False

    def resume(self) -> bool:
        """继续训练任务（重新执行训练脚本并加上--resume True参数）"""
        try:
            if not self.task_info:
                logger.warning("没有任务信息，无法继续训练")
                return False

            # 通过SSH连接到服务器并重新启动训练
            if self.ssh_client:
                try:
                    # 构建继续训练的命令
                    # 获取挂载路径配置，与开始训练时保持一致
                    user_data_mount_path = env('SITON_DATA_MOUNT_PATH', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9')

                    # 构建模型和数据集目录
                    if "siton-data-" in user_data_mount_path:
                        model_mount_path = f"{user_data_mount_path}/models"
                        dataset_mount_path = f"{user_data_mount_path}/datasets"
                    else:
                        model_mount_path = user_data_mount_path
                        dataset_mount_path = user_data_mount_path

                    if not model_mount_path:
                        model_mount_path = "/root/siton-data/models"

                    # 拼接最终的数据集和模型路径，与开始训练时保持一致
                    dataset_path = f"{dataset_mount_path}/{self.training_task.dataset_name}/data.yaml"
                    model_path = f"{model_mount_path}/{self.training_task.model_path}"
                    config_path = '/workspace/training_config.json'
                    task_id = str(self.training_task.id)

                    # 设置环境变量命令
                    set_env_cmd = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH'

                    # 构建继续训练命令，添加--resume True参数
                    resume_command = (
                        f"{set_env_cmd} && "
                        f"cd /workspace && "
                        f"python train_yolov8_multi.py "
                        f"--model {model_path} "
                        f"--config {config_path} "
                        f"--data {dataset_path} "
                        f"--task_id task_{task_id} "
                        f"--resume True "
                        f"> /workspace/train.log 2>&1 &"
                    )

                    logger.info(f"执行继续训练命令: {resume_command}")

                    # 执行继续训练命令
                    stdin, stdout, stderr = self.ssh_client.exec_command(resume_command)

                    # 等待命令执行完成
                    exit_status = stdout.channel.recv_exit_status()

                    if exit_status == 0:
                        logger.info("继续训练命令执行成功")

                        # 等待一段时间，然后检查进程是否启动
                        time.sleep(3)

                        # 检查训练进程是否启动
                        stdin, stdout, stderr = self.ssh_client.exec_command(
                            "ps aux | grep train_yolov8_multi.py | grep -v grep"
                        )
                        processes = stdout.read().decode().strip()

                        if processes:
                            logger.info(f"训练进程已启动: {processes}")
                            return True
                        else:
                            logger.warning("训练进程未能启动")
                            return False
                    else:
                        error_output = stderr.read().decode().strip()
                        logger.error(f"继续训练命令执行失败，退出状态: {exit_status}, 错误: {error_output}")
                        return False

                except Exception as ssh_error:
                    logger.error(f"SSH执行继续训练命令失败: {ssh_error}")
                    return False
            else:
                logger.error("SSH连接不可用")
                return False

        except Exception as e:
            logger.error(f"继续训练任务失败: {e}")
            return False

    def get_task_id(self) -> Optional[str]:
        """获取任务ID"""
        return self.task_info.get('taskId') if self.task_info else None

    def get_task_info(self) -> Optional[Dict]:
        """获取完整的任务信息"""
        if not self.task_info:
            return None
            
        try:
            # 从资源调度平台获取最新的任务信息
            task_info = self.resource_manager.query_task(self.task_info['taskId'])
            if task_info and task_info.get('records'):
                return task_info['records'][0]
            return None
        except Exception as e:
            logger.error(f"获取任务信息失败: {e}")
            return self.task_info
            
    def get_training_models_info(self) -> List[Dict]:
        """
        从远程服务器获取training_module.json文件中的模型信息
        
        Returns:
            List[Dict]: 模型信息列表，每个字典包含模型的详细信息
                        如果获取失败，返回空列表
        """
        if not self.task_info or not self.ssh_client:
            logger.warning("无法获取模型信息：任务未启动或SSH未连接")
            return []
            
        try:
            # 轮询检查training_module.json文件是否存在且包含om_path,后续通过om_path进行推理
            max_retries = 10
            retry_interval = 2  # 2秒间隔
            
            for attempt in range(max_retries):
                # 检查文件是否存在
                check_cmd = f"test -f {self.remote_dir}/training_module.json && echo 'exists' || echo 'not exists'"
                _, stdout, _ = self.ssh_client.exec_command(check_cmd)
                result = stdout.read().decode().strip()
                
                if result == 'exists':
                    # 检查文件内容是否包含om_path
                    read_cmd = f"cat {self.remote_dir}/training_module.json"
                    _, stdout, _ = self.ssh_client.exec_command(read_cmd)
                    content = stdout.read().decode()
                    
                    try:
                        # 解析JSON内容
                        models_data = json.loads(content)
                        # 检查是否是列表且包含至少两个模型
                        if isinstance(models_data, list) and len(models_data) >= 2:
                            # 检查每个模型是否都包含om_path
                            if all('om_path' in model for model in models_data):
                                break
                    except json.JSONDecodeError:
                        pass
                        
                if attempt < max_retries - 1:
                    logger.info(f"第{attempt + 1}次检查: 等待所有模型转换完成...")
                    time.sleep(retry_interval)
                    
            # 最后一次检查文件是否存在
            check_cmd = f"test -f {self.remote_dir}/training_module.json && echo 'exists' || echo 'not exists'"
            _, stdout, _ = self.ssh_client.exec_command(check_cmd)
            result = stdout.read().decode().strip()
            
            if result != 'exists':
                logger.warning("训练模型信息文件不存在")
                return []
                
            # 读取训练模型信息文件
            read_cmd = f"cat {self.remote_dir}/training_module.json"
            _, stdout, _ = self.ssh_client.exec_command(read_cmd)
            
            # 解析JSON数据
            models_json = stdout.read().decode()
            if not models_json:
                logger.warning("训练模型信息文件为空")
                return []
                
            try:
                models_data = json.loads(models_json)
                
                # 为每个模型添加服务器信息
                for model in models_data:
                    # 添加服务器IP和密码信息，方便后续访问
                    model['server_ip'] = self.task_info['ip']
                    model['server_port'] = self.task_info['port']
                    model['server_password'] = self.task_info['password']
                    
                logger.info(f"成功获取 {len(models_data)} 个训练模型信息")
                return models_data
                
            except json.JSONDecodeError as e:
                logger.error(f"解析训练模型信息失败: {e}")
                return []
                
        except Exception as e:
            logger.error(f"获取训练模型信息失败: {e}")
            return []

    def schedule_model_save_task(self):
        """
        调度异步任务保存模型信息，避免阻塞SSE流程
        """
        try:
            import threading

            def delayed_save():
                """延迟保存模型信息的后台任务"""
                try:
                    # 等待一段时间确保文件生成
                    logger.info("等待30秒确保模型信息文件完全生成...")
                    time.sleep(30)

                    # 尝试保存模型信息
                    max_retries = 3
                    for attempt in range(max_retries):
                        logger.info(f"第{attempt+1}次尝试保存模型信息到数据库...")

                        if self.save_training_models_to_db():
                            logger.info("模型信息保存成功")
                            break
                        else:
                            if attempt < max_retries - 1:
                                logger.warning(f"第{attempt+1}次保存失败，10秒后重试...")
                                time.sleep(10)
                            else:
                                logger.error("所有保存尝试都失败了")

                except Exception as e:
                    logger.error(f"异步保存模型信息失败: {e}")

            # 启动后台线程
            save_thread = threading.Thread(target=delayed_save, daemon=True)
            save_thread.start()
            logger.info("已启动后台线程保存模型信息")

        except Exception as e:
            logger.error(f"启动异步保存任务失败: {e}")
            # 如果异步失败，尝试同步保存一次
            try:
                self.save_training_models_to_db()
            except:
                logger.error("同步保存也失败了")

    def save_training_models_to_db(self):
        """
        将训练模型信息保存到数据库
        
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        try:
            # 导入模型
            from backend_api.models.training_model import TrainingModel
            from django.utils import timezone
            
            # 获取训练模型信息
            models_data = self.get_training_models_info()
            if not models_data:
                logger.warning("没有模型数据可保存")
                return False
                
            # 记录保存的模型数量
            saved_count = 0
            
            # 处理每个模型信息
            for model_info in models_data:
                # 检查模型是否已存在
                model_name = model_info.get('model_name', '')
                existing_model = TrainingModel.objects.filter(
                    task=self.training_task,
                    model_name=model_name
                ).first()
                
                # 提取模型信息
                model_path = model_info.get('model_path', '')
                accuracy = float(model_info.get('accuracy', 0.0))
                precision = float(model_info.get('precision', 0.0))
                recall = float(model_info.get('recall', 0.0))
                inference_speed = float(model_info.get('inference_speed', 0.0))
                model_size_mb = float(model_info.get('model_size_mb', 0.0))
                is_best = bool(model_info.get('is_best', False))
                epoch = int(model_info.get('epoch', 0))
                server_ip = model_info.get('server_ip', '')
                server_port = model_info.get('server_port', '')
                server_password = model_info.get('server_password', '')
                converted_model_path = model_info.get('om_path', None)
                
                # 设置默认值
                is_converted = False
                conversion_status = 'pending'
                export_status = 'pending'
                
                if existing_model:
                    # 更新现有模型
                    existing_model.model_path = model_path
                    existing_model.accuracy = accuracy
                    existing_model.precision = precision
                    existing_model.recall = recall
                    existing_model.inference_speed = inference_speed
                    existing_model.model_size_mb = model_size_mb
                    existing_model.is_best = is_best
                    existing_model.epoch = epoch
                    existing_model.converted_model_path = converted_model_path
                    existing_model.server_ip = server_ip
                    existing_model.server_port = server_port
                    existing_model.server_password = server_password
                    existing_model.updated_time = timezone.now()
                    existing_model.save()
                    logger.info(f"更新模型记录: {model_name}")
                else:
                    # 创建新模型记录
                    TrainingModel.objects.create(
                        task=self.training_task,
                        model_name=model_name,
                        model_path=model_path,
                        accuracy=accuracy,
                        precision=precision,
                        recall=recall,
                        inference_speed=inference_speed,
                        model_size_mb=model_size_mb,
                        is_best=is_best,
                        epoch=epoch,
                        server_ip=server_ip,
                        server_port=server_port,
                        server_password=server_password,
                        is_converted=is_converted,
                        conversion_status=conversion_status,
                        converted_model_path=converted_model_path,   
                        export_status=export_status,
                        created_by=getattr(self.training_task, 'created_by', None),
                        created_time=timezone.now(),
                        updated_time=timezone.now()
                    )
                    logger.info(f"创建新模型记录: {model_name}")
                
                saved_count += 1
                
            logger.info(f"已保存 {saved_count} 条模型记录")
            return saved_count > 0
            
        except Exception as e:
            logger.error(f"保存训练模型到数据库失败: {e}")
            return False