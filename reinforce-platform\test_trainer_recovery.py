#!/usr/bin/env python3
"""
测试训练器恢复机制
用于验证服务重启后训练器的恢复功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'reinforce_platform.settings')
django.setup()

from backend_api.models.training import TrainingTask
from backend_api.views.training import get_or_create_trainer, active_trainers, cleanup_inactive_trainers
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_trainer_recovery():
    """测试训练器恢复机制"""
    
    print("=== 训练器恢复机制测试 ===")
    
    # 1. 清空当前活跃训练器（模拟服务重启）
    print(f"当前活跃训练器数量: {len(active_trainers)}")
    active_trainers.clear()
    print("已清空活跃训练器（模拟服务重启）")
    
    # 2. 查找运行中的训练任务
    running_tasks = TrainingTask.objects.filter(status__in=['running', 'pending'])
    print(f"发现 {running_tasks.count()} 个运行中的训练任务")
    
    for task in running_tasks:
        print(f"\n--- 测试任务 {task.id} ---")
        print(f"任务状态: {task.status}")
        print(f"创建时间: {task.created_at}")
        
        # 3. 尝试恢复训练器
        trainer = get_or_create_trainer(task.id)
        
        if trainer:
            print(f"✅ 成功恢复训练器: {task.id}")
            print(f"当前活跃训练器数量: {len(active_trainers)}")
        else:
            print(f"❌ 无法恢复训练器: {task.id}")
            
    # 4. 测试清理机制
    print(f"\n--- 测试清理机制 ---")
    print(f"清理前活跃训练器数量: {len(active_trainers)}")
    cleanup_inactive_trainers()
    print(f"清理后活跃训练器数量: {len(active_trainers)}")
    
    # 5. 显示最终状态
    print(f"\n=== 测试完成 ===")
    print(f"最终活跃训练器: {list(active_trainers.keys())}")

def test_database_consistency():
    """测试数据库状态一致性"""
    
    print("\n=== 数据库状态一致性测试 ===")
    
    # 统计各状态的任务数量
    status_counts = {}
    for status_choice in TrainingTask.STATUS_CHOICES:
        status = status_choice[0]
        count = TrainingTask.objects.filter(status=status).count()
        status_counts[status] = count
        print(f"{status_choice[1]}: {count}")
    
    # 检查可能需要恢复的任务
    recoverable_tasks = TrainingTask.objects.filter(status__in=['running', 'pending'])
    print(f"\n可能需要恢复的任务: {recoverable_tasks.count()}")
    
    for task in recoverable_tasks:
        print(f"- 任务 {task.id}: {task.status}, 创建于 {task.created_at}")

if __name__ == "__main__":
    try:
        test_database_consistency()
        test_trainer_recovery()
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
