#!/usr/bin/env python3
"""
测试服务器信息保存功能
验证训练任务启动后是否正确保存服务器信息到数据库
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'reinforce_platform.settings')
django.setup()

from backend_api.models.training import TrainingTask
from backend_api.views.training import get_or_create_trainer
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_server_info_save():
    """测试服务器信息保存功能"""
    
    print("=== 服务器信息保存功能测试 ===")
    
    # 查找有服务器信息的训练任务
    tasks_with_server_info = TrainingTask.objects.filter(
        server_ip__isnull=False,
        server_port__isnull=False
    )
    
    print(f"发现 {tasks_with_server_info.count()} 个包含服务器信息的训练任务")
    
    for task in tasks_with_server_info[:5]:  # 只显示前5个
        print(f"\n--- 任务 {task.id} ---")
        print(f"状态: {task.status}")
        print(f"服务器IP: {task.server_ip}")
        print(f"服务器端口: {task.server_port}")
        print(f"服务器密码: {'***' if task.server_password else '无'}")
        
        if hasattr(task, 'server_info') and task.server_info:
            print(f"详细信息: {task.server_info}")
        
        # 测试训练器恢复
        if task.status in ['running', 'pending']:
            print("尝试恢复训练器...")
            trainer = get_or_create_trainer(task.id)
            if trainer:
                print("✅ 训练器恢复成功")
            else:
                print("❌ 训练器恢复失败")

def test_server_info_structure():
    """测试服务器信息数据结构"""
    
    print("\n=== 服务器信息数据结构测试 ===")
    
    # 查找包含完整服务器信息的任务
    tasks_with_full_info = TrainingTask.objects.filter(
        server_info__isnull=False
    ).exclude(server_info={})
    
    print(f"发现 {tasks_with_full_info.count()} 个包含完整服务器信息的训练任务")
    
    for task in tasks_with_full_info[:3]:  # 只显示前3个
        print(f"\n--- 任务 {task.id} 服务器信息结构 ---")
        server_info = task.server_info
        
        required_fields = ['ip', 'port', 'password', 'taskId']
        optional_fields = ['username', 'created_at', 'trainer_type']
        
        print("必需字段:")
        for field in required_fields:
            value = server_info.get(field, '缺失')
            print(f"  {field}: {value}")
        
        print("可选字段:")
        for field in optional_fields:
            value = server_info.get(field, '缺失')
            print(f"  {field}: {value}")

def show_statistics():
    """显示统计信息"""
    
    print("\n=== 统计信息 ===")
    
    total_tasks = TrainingTask.objects.count()
    tasks_with_ip = TrainingTask.objects.filter(server_ip__isnull=False).count()
    tasks_with_full_info = TrainingTask.objects.filter(server_info__isnull=False).exclude(server_info={}).count()
    
    print(f"总训练任务数: {total_tasks}")
    print(f"包含服务器IP的任务: {tasks_with_ip}")
    print(f"包含完整服务器信息的任务: {tasks_with_full_info}")
    
    if total_tasks > 0:
        print(f"服务器信息覆盖率: {tasks_with_ip/total_tasks*100:.1f}%")

if __name__ == "__main__":
    try:
        show_statistics()
        test_server_info_save()
        test_server_info_structure()
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
