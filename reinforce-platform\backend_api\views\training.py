import json
import logging
import threading
import time
import hashlib
from datetime import datetime
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.http import StreamingHttpResponse, JsonResponse
from django.views import View

from backend_api.models.training import TrainingTask, DLTrainingConfig
from backend_api.serializers.training import (
    TrainingRequestSerializer,
    DLConfigSaveSerializer,
    DLConfigImportSerializer,
    DLTrainingConfigSerializer
)
from utils.yolov8_docker_trainer import YOLOv8DockerTrainer

logger = logging.getLogger(__name__)

# 存储活跃的训练任务
active_trainers = {}

def get_or_create_trainer(training_id):
    """
    获取或创建训练器实例，支持服务重启后的恢复

    Args:
        training_id: 训练任务ID

    Returns:
        trainer: 训练器实例，如果任务已结束返回None
    """
    try:
        # 首先从内存中查找
        trainer = active_trainers.get(training_id)
        if trainer:
            return trainer

        # 如果内存中没有，从数据库检查任务状态
        from backend_api.models.training import TrainingTask
        try:
            training_task = TrainingTask.objects.get(id=training_id)
        except TrainingTask.DoesNotExist:
            logger.warning(f"训练任务不存在: {training_id}")
            return None

        # 检查任务是否应该有活跃的训练器
        if training_task.status in ['running', 'pending']:
            logger.info(f"服务重启后恢复训练器: {training_id}, 状态: {training_task.status}")

            # 重新创建训练器实例
            from utils.yolov8_docker_trainer import YOLOv8DockerTrainer
            trainer = YOLOv8DockerTrainer(training_task)

            # 尝试重新连接到现有的训练任务
            if trainer.reconnect_to_existing_task():
                active_trainers[training_id] = trainer
                logger.info(f"成功重新连接到训练任务: {training_id}")
                return trainer
            else:
                logger.warning(f"无法重新连接到训练任务: {training_id}，可能任务已结束")
                # 更新数据库状态为失败
                training_task.status = 'failed'
                training_task.save()
                return None
        else:
            # 任务已结束，不需要训练器
            logger.debug(f"训练任务已结束: {training_id}, 状态: {training_task.status}")
            return None

    except Exception as e:
        logger.error(f"获取或创建训练器失败: {training_id}, 错误: {e}")
        return None

def cleanup_inactive_trainers():
    """
    清理不活跃的训练器，释放资源
    """
    try:
        from backend_api.models.training import TrainingTask

        # 获取所有已结束的任务ID
        finished_task_ids = set(
            TrainingTask.objects.filter(
                status__in=['completed', 'failed', 'cancelled']
            ).values_list('id', flat=True)
        )

        # 清理对应的训练器
        inactive_trainer_ids = []
        for training_id in list(active_trainers.keys()):
            if training_id in finished_task_ids:
                inactive_trainer_ids.append(training_id)

        for training_id in inactive_trainer_ids:
            trainer = active_trainers.pop(training_id, None)
            if trainer:
                try:
                    trainer.cleanup()  # 清理资源
                except:
                    pass
                logger.info(f"清理不活跃的训练器: {training_id}")

        if inactive_trainer_ids:
            logger.info(f"清理了 {len(inactive_trainer_ids)} 个不活跃的训练器")

    except Exception as e:
        logger.error(f"清理不活跃训练器失败: {e}")

def start_training_in_background(training_task, trainer):
    """在后台启动训练任务"""
    try:
        logger.info(f"在后台启动训练任务: {training_task.id}")
        success = trainer.start()

        if success:
            # 保存服务器信息到数据库（用于后续恢复和访问）
            if hasattr(trainer, 'task_info') and trainer.task_info:
                try:
                    task_info = trainer.task_info
                    training_task.server_ip = task_info.get('ip', '')
                    training_task.server_port = task_info.get('port', '')
                    training_task.server_password = task_info.get('password', '')

                    # 保存完整的服务器信息到JSON字段
                    server_info = {
                        'ip': task_info.get('ip', ''),
                        'port': task_info.get('port', ''),
                        'password': task_info.get('password', ''),
                        'taskId': task_info.get('taskId', ''),
                        'username': task_info.get('username', 'root'),
                        'created_at': timezone.now().isoformat(),
                        'trainer_type': 'YOLOv8DockerTrainer'
                    }
                    training_task.server_info = server_info

                    logger.info(f"已保存服务器信息到数据库: 任务{training_task.id}, IP: {task_info.get('ip')}, 端口: {task_info.get('port')}")
                except Exception as save_error:
                    logger.error(f"保存服务器信息失败: {save_error}")

            # 更新任务状态为运行中
            training_task.status = 'running'
            training_task.start_time = timezone.now()
            training_task.save()
            logger.info(f"训练任务 {training_task.id} 启动成功")
        else:
            # 启动失败，更新任务状态
            training_task.status = 'failed'
            training_task.save()
            logger.error(f"训练任务 {training_task.id} 启动失败")

    except Exception as e:
        logger.error(f"启动训练任务 {training_task.id} 时出现异常: {str(e)}")
        training_task.status = 'failed'
        training_task.save()

def stop_training_in_background(training_id, training_task, trainer):
    """在后台停止训练任务"""
    try:
        logger.info(f"在后台停止训练任务: {training_id}")
        success = trainer.stop()

        if success:
            # 停止成功，从活跃训练器列表中移除
            if training_id in active_trainers:
                del active_trainers[training_id]

            # 更新任务状态
            training_task.status = 'cancelled'
            training_task.end_time = timezone.now()
            training_task.save()
            logger.info(f"训练任务 {training_id} 取消成功，状态已更新为cancelled")
        else:
            logger.warning(f"无法取消训练任务 {training_id}，状态保持为cancelling")
            # 停止失败，但保持cancelling状态，让用户知道操作正在进行
            # 不要立即改为failed，因为可能需要重试

    except Exception as e:
        logger.error(f"停止训练任务 {training_id} 时出现异常: {str(e)}")
        # 出现异常时，设置为failed状态
        training_task.status = 'failed'
        training_task.save()
        logger.info(f"训练任务 {training_id} 因异常设置为failed状态")

class TrainingStartView(APIView):
    """启动训练任务的视图"""
    
    def post(self, request):
        """启动训练任务"""
        serializer = TrainingRequestSerializer(data=request.data)
        if serializer.is_valid():
            try:
                # 创建训练任务记录
                training_task = serializer.save()
                training_task.status = 'pending'  # 将状态设置为等待中
                training_task.save()

                # 后续此次增加不同的训练任务调用不同的训练方法，目前只支持YOLOv8的训练
                
                # 清理不活跃的训练器
                cleanup_inactive_trainers()

                # 初始化YOLOv8训练器
                trainer = YOLOv8DockerTrainer(training_task)
                active_trainers[training_task.id] = trainer
                
                # 在后台线程中启动训练
                thread = threading.Thread(
                    target=start_training_in_background,
                    args=(training_task, trainer)
                )
                thread.daemon = True  # 守护线程，主程序退出时会自动结束
                thread.start()
                
                # 立即返回响应
                return Response({
                    'success': True,
                    'message': '训练任务已提交',
                    'status': "running",
                    'trainingId': training_task.id
                }, status=status.HTTP_202_ACCEPTED)
                
            except Exception as e:
                logger.error(f"提交训练失败: {str(e)}")
                # 发生异常时，确保更新任务状态
                if 'training_task' in locals():
                    training_task.status = 'failed'
                    training_task.save()
                return Response({
                    'success': False,
                    'message': f'提交训练失败: {str(e)}',
                    'status': "failed"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'success': False,
            'message': '请求参数无效',
            'status': "failed",
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

class TrainingMetricsView(APIView):
    """获取训练指标的视图"""

    def get(self, request, training_id):
        """获取训练指标"""
        try:
            training_task = get_object_or_404(TrainingTask, id=training_id)
            trainer = get_or_create_trainer(training_id)

            # 初始化返回数据
            response_data = {
                'success': True,
                'status': training_task.status,
                'loss': {
                    'epochs': [],
                    'train_loss': [],
                    'val_loss': [],
                    'timestamps': []
                },
                'resources': {
                    'cpuUsage': [],
                    'gpuUsage': [],
                    'npuUsage': [],
                    'memoryUsage': []
                },
                'metrics': {
                    'precision': 0.0,
                    'recall': 0.0,
                    'mAP50': 0.0,
                    'mAP50-95': 0.0
                }
            }

            if trainer:
                # 更新训练任务状态
                task_status = trainer.get_task_status()
                if task_status and 'status' in task_status:
                    old_status = training_task.status
                    training_task.status = task_status['status']
                    training_task.save()
                    response_data['status'] = training_task.status

                    # 如果状态变为完成，使用更强的数据获取策略
                    if old_status != 'completed' and training_task.status == 'completed':
                        logger.info(f"训练任务 {training_id} 刚刚完成，使用强化数据获取策略")
                        # 等待一小段时间确保文件完全写入
                        time.sleep(2)
                        metrics_data = trainer.get_training_metrics(max_retries=5)
                    else:
                        # 直接从服务器获取训练指标数据
                        metrics_data = trainer.get_training_metrics()
                else:
                    metrics_data = trainer.get_training_metrics()

                # 如果获取到指标数据，更新响应数据
                if metrics_data:
                    response_data['loss'] = {
                        'epochs': metrics_data.get('epochs', []),
                        'train_loss': metrics_data.get('train_losses', []),
                        'val_loss': metrics_data.get('val_losses', []),
                        'timestamps': [
                            # 将时间戳转换为ISO格式字符串
                            timestamp.isoformat() if hasattr(timestamp, 'isoformat')
                            else str(timestamp)
                            for timestamp in metrics_data.get('timestamps', [])
                        ]
                    }

                    response_data['resources'] = {
                        'cpuUsage': metrics_data.get('cpu_usages', []),
                        'gpuUsage': metrics_data.get('gpu_usages', []),
                        'npuUsage': metrics_data.get('npu_usages', []),
                        'memoryUsage': metrics_data.get('memory_usages', [])
                    }

                    response_data['metrics'] = {
                        'precision': metrics_data.get('precision', 0.0),
                        'recall': metrics_data.get('recall', 0.0),
                        'mAP50': metrics_data.get('mAP50', 0.0),
                        'mAP50-95': metrics_data.get('mAP50-95', 0.0)
                    }

                    epoch_count = len(metrics_data.get('epochs', []))
                    logger.info(f"成功获取训练任务 {training_id} 的指标数据，包含 {epoch_count} 个epoch")

                    # 如果是完成状态，验证数据完整性
                    if training_task.status == 'completed':
                        expected_epochs = int(training_task.epochs)
                        if epoch_count < expected_epochs:
                            logger.warning(f"训练任务 {training_id} 数据可能不完整：{epoch_count}/{expected_epochs} epochs")
                        else:
                            logger.info(f"训练任务 {training_id} 数据完整：{epoch_count}/{expected_epochs} epochs")
                else:
                    logger.warning(f"训练任务 {training_id} 暂无指标数据")
            else:
                logger.warning(f"训练任务 {training_id} 的训练器不存在或未激活")

            return Response(response_data)

        except Exception as e:
            logger.error(f"获取训练指标失败: {e}")
            return Response({
                'success': False,
                'error': str(e),
                'status': "failed"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingMetricsStreamView(View):
    """训练指标实时推送视图 (SSE)"""

    def get(self, request, training_id):
        """GET方式建立SSE连接（token通过URL参数传递）"""
        return self._handle_sse_connection(request, training_id)

    def post(self, request, training_id):
        """POST方式建立SSE连接（token通过请求体传递，适用于长token）"""
        return self._handle_sse_connection(request, training_id)

    def _handle_sse_connection(self, request, training_id):
        """建立SSE连接，实时推送训练指标"""

        # 记录请求信息用于调试
        accept_header = request.META.get('HTTP_ACCEPT', '')
        logger.info(f"SSE请求Accept头: {accept_header}")
        logger.info(f"SSE请求方法: {request.method}")
        logger.info(f"SSE请求路径: {request.path}")

        # 暂时移除Accept头检查，专注解决token验证问题
        # 后续可以根据需要重新启用
        # if accept_header and not any(x in accept_header for x in ['text/event-stream', 'text/plain', '*/*', 'text/*']):
        #     from django.http import JsonResponse
        #     return JsonResponse({
        #         'error': 'This endpoint only supports Server-Sent Events. Please use EventSource or set Accept header to text/event-stream',
        #         'expected_accept': 'text/event-stream',
        #         'received_accept': accept_header
        #     }, status=406)

        # 支持通过URL参数传递token（用于EventSource）
        token = request.GET.get('token')
        if not token:
            logger.error("SSE连接缺少token参数")
            return JsonResponse({
                'error': 'Missing token',
                'detail': 'Token parameter is required for SSE connection'
            }, status=401)

        if token:
            from rest_framework_simplejwt.authentication import JWTAuthentication
            from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
            import urllib.parse

            try:
                # 处理可能的URL编码
                decoded_token = urllib.parse.unquote(token)

                # 去掉可能的 "Bearer " 前缀
                if decoded_token.startswith('Bearer '):
                    decoded_token = decoded_token[7:]

                # 去掉首尾空格
                decoded_token = decoded_token.strip()

                # 验证token长度（JWT token通常很长）
                if len(decoded_token) < 50:
                    logger.error(f"Token长度不足: {len(decoded_token)}, token: {decoded_token[:20]}...")
                    return JsonResponse({
                        'error': 'Invalid token format',
                        'detail': f'Token too short: {len(decoded_token)} characters'
                    }, status=401)

                # 记录token信息用于调试
                logger.info(f"尝试验证token，长度: {len(decoded_token)}, 前20字符: {decoded_token[:20]}...")

                jwt_auth = JWTAuthentication()
                validated_token = jwt_auth.get_validated_token(decoded_token)
                user = jwt_auth.get_user(validated_token)
                request.user = user

                logger.info(f"SSE认证成功，用户: {user.username}, 训练任务: {training_id}")

            except (InvalidToken, TokenError) as e:
                logger.error(f"SSE token验证失败: {str(e)}, token长度: {len(token)}, 处理后长度: {len(decoded_token) if 'decoded_token' in locals() else 'N/A'}")
                return JsonResponse({
                    'error': 'Invalid token',
                    'detail': str(e),
                    'token_length': len(token),
                    'processed_token_length': len(decoded_token) if 'decoded_token' in locals() else None
                }, status=401)
            except Exception as e:
                logger.error(f"SSE认证过程中发生错误: {str(e)}")
                return JsonResponse({
                    'error': 'Authentication error',
                    'detail': str(e)
                }, status=401)

        def event_stream():
            """SSE事件流生成器"""
            try:
                training_task = get_object_or_404(TrainingTask, id=training_id)
                trainer = get_or_create_trainer(training_id)

                last_metrics_hash = None
                heartbeat_counter = 0
                final_sent = False  # 标记是否已发送最终数据

                logger.info(f"开始SSE推送训练指标，任务ID: {training_id}")

                # 发送初始连接确认
                yield f"event: connected\ndata: {json.dumps({'message': 'Connected to training metrics stream', 'training_id': training_id})}\n\n"

                while True:
                    try:
                        # 重新获取训练任务状态（可能在其他地方被更新）
                        training_task.refresh_from_db()

                        # 检查训练是否已完成，确保只发送一次最终数据
                        if training_task.status in ['completed', 'failed', 'cancelled'] and not final_sent:
                            logger.info(f"检测到训练任务 {training_id} 状态为 {training_task.status}，准备发送最终数据")

                            # 发送最终数据
                            final_data = self.build_final_metrics(training_task, trainer)

                            # 记录最终数据的统计信息
                            if final_data and final_data.get('loss', {}).get('epochs'):
                                epoch_count = len(final_data['loss']['epochs'])
                                logger.info(f"最终数据包含 {epoch_count} 个epoch的训练指标")
                            else:
                                logger.warning(f"最终数据为空或不完整")

                            yield f"event: final\ndata: {json.dumps(final_data)}\n\n"

                            # 发送关闭事件
                            yield f"event: close\ndata: {json.dumps({'message': 'Training finished', 'status': training_task.status})}\n\n"
                            logger.info(f"训练任务 {training_id} 已完成，关闭SSE连接")

                            final_sent = True  # 标记已发送最终数据
                            break

                        # 获取当前指标数据
                        current_data = None
                        if trainer:
                            # 刷新数据库状态，确保使用最新状态
                            training_task.refresh_from_db()

                            # 主动更新训练任务状态（与TrainingMetricsView保持一致）
                            task_status = trainer.get_task_status()
                            if task_status and 'status' in task_status:
                                old_status = training_task.status
                                new_status = task_status['status']

                                # 只有状态发生变化时才更新数据库
                                if old_status != new_status:
                                    logger.info(f"SSE检测到状态变化: {old_status} -> {new_status}")
                                    training_task.status = new_status
                                    training_task.save()

                            # SSE模式：始终获取实时数据
                            metrics_data = trainer.get_training_metrics()

                            # 构建响应数据（使用最新更新的状态）
                            current_data = self.build_response_data(training_task, metrics_data, trainer)
                        else:
                            # 训练器不存在，返回基础数据
                            current_data = self.build_response_data(training_task, {}, None)

                        # 只有在未发送最终数据时才推送常规指标数据
                        if not final_sent:
                            # 计算数据哈希，只在数据变化时推送
                            current_hash = hashlib.md5(json.dumps(current_data, sort_keys=True).encode()).hexdigest()

                            if current_hash != last_metrics_hash:
                                yield f"event: metrics\ndata: {json.dumps(current_data)}\n\n"
                                last_metrics_hash = current_hash
                                logger.debug(f"推送新的训练指标数据，任务ID: {training_id}")

                        # 每30秒发送一次心跳
                        heartbeat_counter += 1
                        if heartbeat_counter >= 6:  # 5秒 * 6 = 30秒
                            yield f"event: heartbeat\ndata: {json.dumps({'timestamp': int(time.time()), 'training_id': training_id})}\n\n"
                            heartbeat_counter = 0

                        time.sleep(5)  # 5秒检查一次

                    except Exception as inner_e:
                        logger.error(f"SSE推送过程中发生错误: {inner_e}")
                        yield f"event: error\ndata: {json.dumps({'error': str(inner_e), 'timestamp': int(time.time())})}\n\n"
                        time.sleep(10)  # 错误后等待更长时间

            except Exception as e:
                logger.error(f"SSE连接初始化失败: {e}")
                yield f"event: error\ndata: {json.dumps({'error': f'Connection failed: {str(e)}', 'timestamp': int(time.time())})}\n\n"

        # 创建SSE响应
        response = StreamingHttpResponse(
            event_stream(),
            content_type='text/event-stream'
        )

        # 设置SSE必要的响应头
        response['Cache-Control'] = 'no-cache'
        #response['Connection'] = 'keep-alive'
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Headers'] = 'Cache-Control'

        return response

    def build_response_data(self, training_task, metrics_data, trainer):
        """构建响应数据"""
        response_data = {
            'success': True,
            'status': training_task.status,
            'timestamp': int(time.time()),
            'loss': {
                'epochs': [],
                'train_loss': [],
                'val_loss': [],
                'timestamps': []
            },
            'resources': {
                'cpuUsage': [],
                'gpuUsage': [],
                'npuUsage': [],
                'memoryUsage': []
            },
            'metrics': {
                'precision': 0.0,
                'recall': 0.0,
                'mAP50': 0.0,
                'mAP50-95': 0.0
            }
        }

        # 如果有指标数据，更新响应数据
        if metrics_data:
            response_data['loss'] = {
                'epochs': metrics_data.get('epochs', []),
                'train_loss': metrics_data.get('train_losses', []),
                'val_loss': metrics_data.get('val_losses', []),
                'timestamps': [
                    # 将时间戳转换为ISO格式字符串
                    timestamp.isoformat() if hasattr(timestamp, 'isoformat')
                    else str(timestamp)
                    for timestamp in metrics_data.get('timestamps', [])
                ]
            }

            response_data['resources'] = {
                'cpuUsage': metrics_data.get('cpu_usages', []),
                'gpuUsage': metrics_data.get('gpu_usages', []),
                'npuUsage': metrics_data.get('npu_usages', []),
                'memoryUsage': metrics_data.get('memory_usages', [])
            }

            response_data['metrics'] = {
                'precision': metrics_data.get('precision', 0.0),
                'recall': metrics_data.get('recall', 0.0),
                'mAP50': metrics_data.get('mAP50', 0.0),
                'mAP50-95': metrics_data.get('mAP50-95', 0.0)
            }

        return response_data

    def build_final_metrics(self, training_task, trainer):
        """构建最终的指标数据"""
        if trainer:
            logger.info(f"开始获取训练任务 {training_task.id} 的最终指标数据")

            # 等待一小段时间确保文件完全写入
            time.sleep(2)

            # 多次尝试获取最终数据，确保数据完整
            final_metrics = None
            max_attempts = 5

            for attempt in range(max_attempts):
                logger.info(f"第{attempt+1}次尝试获取最终指标数据")

                # 强制刷新获取最终数据
                final_metrics = trainer.get_training_metrics(max_retries=3)

                if final_metrics and final_metrics.get('epochs'):
                    epoch_count = len(final_metrics.get('epochs', []))
                    logger.info(f"第{attempt+1}次尝试成功，获取到 {epoch_count} 个epoch的数据")

                    # 检查是否获取到了完整的训练数据
                    expected_epochs = int(training_task.epochs)
                    if epoch_count >= expected_epochs:
                        logger.info(f"获取到完整的训练数据：{epoch_count}/{expected_epochs} epochs")
                        break
                    else:
                        logger.warning(f"数据可能不完整：{epoch_count}/{expected_epochs} epochs，继续重试")
                else:
                    logger.warning(f"第{attempt+1}次尝试获取最终指标失败")

                if attempt < max_attempts - 1:
                    time.sleep(3)  # 等待3秒后重试

            if not final_metrics:
                logger.error(f"经过 {max_attempts} 次尝试，仍无法获取最终指标数据")
                final_metrics = {}

            return self.build_response_data(training_task, final_metrics, trainer)
        else:
            logger.warning(f"训练任务 {training_task.id} 没有对应的训练器")
            return self.build_response_data(training_task, {}, None)


class TrainingCancelView(APIView):
    """取消训练任务的视图"""
    
    def post(self, request, training_id):
        """取消训练任务"""
        try:
            training_id = int(training_id)
            training_task = get_object_or_404(TrainingTask, id=training_id)
            logger.info(f"正在取消训练任务: {training_id}")
            
            trainer = get_or_create_trainer(training_id)
            if trainer:
                logger.info(f"找到活跃的训练器，任务ID: {training_id}")
                try:
                    # 在后台线程中停止训练
                    thread = threading.Thread(
                        target=stop_training_in_background,
                        args=(training_id, training_task, trainer)
                    )
                    thread.daemon = True  # 守护线程，主程序退出时会自动结束
                    thread.start()
                    
                    # 立即更新状态为"取消中"
                    training_task.status = 'cancelling'
                    training_task.save()
                    
                    # 立即返回响应
                    logger.info(f"训练任务 {training_id} 取消请求已提交")
                    return Response({
                        'success': True,
                        'message': '训练任务取消请求已提交',
                        'status': "cancelling"
                    }, status=status.HTTP_202_ACCEPTED)
                    
                except Exception as cancel_error:
                    logger.error(f"提交取消请求时发生错误: {cancel_error}")
                    return Response({
                        'success': False,
                        'message': f'取消训练请求提交失败: {str(cancel_error)}',
                        'status': "failed"
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                logger.info(f"未找到活跃的训练器，任务ID: {training_id}")
                # 检查任务状态，确认是否确实应该被取消
                if training_task.status in ['running', 'pending']:
                    logger.info(f"任务 {training_id} 状态为 {training_task.status}，但无活跃训练器，可能服务重启导致，直接标记为取消")

                # 如果没有活跃的训练器，直接将任务状态设置为取消
                training_task.status = 'cancelled'
                training_task.end_time = timezone.now()
                training_task.save()
                return Response({
                    'success': True,
                    'message': '训练任务已取消（任务已结束或未启动）',
                    'status': "cancelled"
                })
        
        except ValueError:
            return Response({
                'success': False,
                'message': '无效的训练任务ID',
                'status': "failed"
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"取消视图中发生意外错误: {e}")
            return Response({
                'success': False,
                'message': f'服务器内部错误: {str(e)}',
                'status': "failed"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def pause_training_in_background(training_id, training_task, trainer):
    """在后台暂停训练任务"""
    try:
        logger.info(f"在后台暂停训练任务: {training_id}")
        success = trainer.pause()

        if success:
            # 暂停成功，更新任务状态
            training_task.status = 'paused'
            training_task.save()
            logger.info(f"训练任务 {training_id} 暂停成功")
        else:
            logger.warning(f"暂停训练任务 {training_id} 失败，但仍标记为暂停状态")
            # 即使暂停操作失败，也标记为暂停状态，因为用户已经请求暂停
            # 这样可以避免状态在pausing和running之间跳跃
            training_task.status = 'paused'
            training_task.save()

    except Exception as e:
        logger.error(f"暂停训练任务 {training_id} 时出现异常: {str(e)}")
        # 出现异常时，也标记为暂停状态，保持用户期望的状态
        training_task.status = 'paused'
        training_task.save()


class TrainingPauseView(APIView):
    """暂停训练任务的视图"""

    def post(self, request, training_id):
        """暂停训练任务"""
        try:
            training_id = int(training_id)
            training_task = get_object_or_404(TrainingTask, id=training_id)
            logger.info(f"正在暂停训练任务: {training_id}")

            # 检查任务状态，只有运行中的任务才能暂停
            if training_task.status != 'running':
                return Response({
                    'success': False,
                    'message': f'任务状态为 {training_task.status}，无法暂停',
                    'status': training_task.status
                }, status=status.HTTP_400_BAD_REQUEST)

            trainer = get_or_create_trainer(training_id)
            if trainer:
                logger.info(f"找到活跃的训练器，任务ID: {training_id}")
                try:
                    # 在后台线程中暂停训练
                    thread = threading.Thread(
                        target=pause_training_in_background,
                        args=(training_id, training_task, trainer)
                    )
                    thread.daemon = True
                    thread.start()

                    # 立即更新状态为"暂停中"
                    training_task.status = 'pausing'
                    training_task.save()

                    # 立即返回响应
                    logger.info(f"训练任务 {training_id} 暂停请求已提交")
                    return Response({
                        'success': True,
                        'message': '训练任务暂停请求已提交',
                        'status': "pausing"
                    }, status=status.HTTP_202_ACCEPTED)

                except Exception as pause_error:
                    logger.error(f"提交暂停请求时发生错误: {pause_error}")
                    return Response({
                        'success': False,
                        'message': f'暂停训练请求提交失败: {str(pause_error)}',
                        'status': "failed"
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                logger.info(f"未找到活跃的训练器，任务ID: {training_id}")
                return Response({
                    'success': False,
                    'message': '训练任务未找到或已结束',
                    'status': "not_found"
                }, status=status.HTTP_404_NOT_FOUND)

        except ValueError:
            return Response({
                'success': False,
                'message': '无效的训练任务ID',
                'status': "invalid_id"
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"暂停训练任务失败: {e}")
            return Response({
                'success': False,
                'message': f'暂停训练任务失败: {str(e)}',
                'status': "failed"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def resume_training_in_background(training_id, training_task, trainer):
    """在后台继续训练任务"""
    try:
        logger.info(f"在后台继续训练任务: {training_id}")
        success = trainer.resume()

        if success:
            # 继续训练成功，先设置为resuming状态，让SSE流检测实际状态
            training_task.status = 'resuming'
            training_task.save()
            logger.info(f"训练任务 {training_id} 继续训练命令执行成功，等待训练进程启动")

            # 等待一段时间让训练进程完全启动，然后更新为running状态
            import time
            time.sleep(5)  # 等待5秒

            # 重新检查训练状态
            training_task.refresh_from_db()
            if training_task.status == 'resuming':  # 如果状态还是resuming，说明没有被其他地方更新
                training_task.status = 'running'
                training_task.save()
                logger.info(f"训练任务 {training_id} 状态更新为running")
        else:
            logger.warning(f"无法继续训练任务 {training_id}")
            # 继续训练失败，保持暂停状态
            training_task.status = 'paused'
            training_task.save()

    except Exception as e:
        logger.error(f"继续训练任务 {training_id} 时出现异常: {str(e)}")
        # 出现异常，保持暂停状态
        training_task.status = 'paused'
        training_task.save()


class TrainingResumeView(APIView):
    """继续训练任务的视图"""

    def post(self, request, training_id):
        """继续训练任务"""
        try:
            training_id = int(training_id)
            training_task = get_object_or_404(TrainingTask, id=training_id)
            logger.info(f"正在继续训练任务: {training_id}")

            # 检查任务状态，只有暂停的任务才能继续
            if training_task.status != 'paused':
                return Response({
                    'success': False,
                    'message': f'任务状态为 {training_task.status}，无法继续训练',
                    'status': training_task.status
                }, status=status.HTTP_400_BAD_REQUEST)

            trainer = get_or_create_trainer(training_id)
            if trainer:
                logger.info(f"找到活跃的训练器，任务ID: {training_id}")
                try:
                    # 在后台线程中继续训练
                    thread = threading.Thread(
                        target=resume_training_in_background,
                        args=(training_id, training_task, trainer)
                    )
                    thread.daemon = True
                    thread.start()

                    # 立即更新状态为"恢复中"
                    training_task.status = 'resuming'
                    training_task.save()

                    # 立即返回响应
                    logger.info(f"训练任务 {training_id} 继续训练请求已提交")
                    return Response({
                        'success': True,
                        'message': '训练任务继续训练请求已提交',
                        'status': "resuming"
                    }, status=status.HTTP_202_ACCEPTED)

                except Exception as resume_error:
                    logger.error(f"提交继续训练请求时发生错误: {resume_error}")
                    return Response({
                        'success': False,
                        'message': f'继续训练请求提交失败: {str(resume_error)}',
                        'status': "failed"
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                logger.info(f"未找到活跃的训练器，任务ID: {training_id}")
                return Response({
                    'success': False,
                    'message': '训练任务未找到或已结束',
                    'status': "not_found"
                }, status=status.HTTP_404_NOT_FOUND)

        except ValueError:
            return Response({
                'success': False,
                'message': '无效的训练任务ID',
                'status': "invalid_id"
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"继续训练任务失败: {e}")
            return Response({
                'success': False,
                'message': f'继续训练任务失败: {str(e)}',
                'status': "failed"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 深度学习配置管理视图
class DLTrainingConfigSaveView(APIView):
    """保存深度学习训练配置"""
    
    parser_classes = [JSONParser]
    
    def post(self, request):
        try:
            logger.info(f"Saving DL config from user: {request.user.id}")
            logger.info(f"Request data: {request.data}")
            logger.info(f"Request content type: {request.content_type}")
            
            # 检查请求数据是否为空
            if not request.data:
                logger.warning("Empty request data received")
                return Response({
                    "success": False,
                    "error": {
                        "code": "EMPTY_DATA",
                        "message": "请求数据为空，请确保发送了有效的JSON数据"
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查配置名称字段（支持多种字段名）
            config_name = None
            for field_name in ['configName', 'config_name', 'name']:
                if field_name in request.data:
                    config_name = request.data[field_name]
                    break
            
            if not config_name:
                # 如果没有找到配置名称，尝试从算法配置中生成一个默认名称
                algorithm_config = request.data.get('algorithm', {})
                if algorithm_config:
                    default_name = f"DL配置_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    logger.info(f"No config name provided, using default: {default_name}")
                    request.data['configName'] = default_name
                else:
                    logger.warning(f"Missing configName in request data: {list(request.data.keys())}")
                    return Response({
                        "success": False,
                        "error": {
                            "code": "MISSING_CONFIG_NAME",
                            "message": "缺少必需字段 'configName'，或者请求数据格式不正确",
                            "details": {
                                "received_fields": list(request.data.keys()),
                                "expected_fields": ["configName", "algorithm", "training"],
                                "solutions": [
                                    "在请求JSON中添加 'configName' 字段",
                                    "确保Content-Type为application/json",
                                    "检查请求数据格式是否正确"
                                ],
                                "example": {
                                    "configName": "我的配置名称",
                                    "description": "配置描述",
                                    "algorithm": {"version": "v8", "modelPath": "/path/to/model"},
                                    "training": {"dataset": {"id": 1, "name": "dataset"}}
                                }
                            }
                        }
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                # 统一使用 configName 字段
                request.data['configName'] = config_name
            
            serializer = DLConfigSaveSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"Invalid config save request: {serializer.errors}")
                return Response({
                    "success": False,
                    "error": {
                        "code": "VALIDATION_ERROR",
                        "message": "请求参数验证失败",
                        "details": serializer.errors,
                        "help": {
                            "configName": "配置名称（必需字段）",
                            "description": "配置描述（可选）",
                            "algorithm": "算法配置（必需）",
                            "training": "训练配置（必需）",
                            "resources": "资源配置（可选）",
                            "parameters": "参数配置（可选）",
                            "otherParams": "其他参数配置（可选）"
                        }
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
            data = serializer.validated_data
            
            # 检查配置名称是否已存在
            if DLTrainingConfig.objects.filter(
                config_name=data['configName'],
                created_by=request.user
            ).exists():
                return Response({
                    "success": False,
                    "error": {
                        "code": "CONFIG_EXISTS",
                        "message": "配置名称已存在，请使用其他名称"
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 创建配置记录
            config = DLTrainingConfig.objects.create(
                config_name=data['configName'],
                description=data.get('description', ''),
                algorithm_config=data['algorithm'],
                training_config=data['training'],
                resources_config=data.get('resources', {}),
                parameters_config=data.get('parameters', {}),
                other_params_config=data.get('otherParams', {}),
                created_by=request.user
            )
            
            logger.info(f"Successfully saved config: {config.config_id}")
            return Response({
                "success": True,
                "data": {
                    "configId": config.config_id,
                    "configName": config.config_name,
                    "message": "配置保存成功"
                }
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Failed to save DL config: {e}", exc_info=True)
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DLTrainingConfigImportView(APIView):
    """批量导入深度学习配置"""
    
    parser_classes = [MultiPartParser]
    
    def post(self, request):
        try:
            logger.info(f"Importing DL config from user: {request.user.id}")
            
            serializer = DLConfigImportSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"Invalid config import request: {serializer.errors}")
                return Response({
                    "success": False,
                    "error": {
                        "code": "VALIDATION_ERROR",
                        "message": "请求参数验证失败",
                        "details": serializer.errors
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
            config_file = serializer.validated_data['config']
            
            try:
                # 读取并解析JSON配置
                config_content = config_file.read().decode('utf-8')
                config_data = json.loads(config_content)
                
                # 验证配置格式
                required_fields = ['configName', 'algorithm', 'training']
                for field in required_fields:
                    if field not in config_data:
                        return Response({
                            "success": False,
                            "error": {
                                "code": "INVALID_CONFIG",
                                "message": f"配置文件缺少必需字段: {field}"
                            }
                        }, status=status.HTTP_400_BAD_REQUEST)
                
                # 检查配置名称是否已存在
                if DLTrainingConfig.objects.filter(
                    config_name=config_data['configName'],
                    created_by=request.user
                ).exists():
                    return Response({
                        "success": False,
                        "error": {
                            "code": "CONFIG_EXISTS",
                            "message": "配置名称已存在，请修改配置名称后重新导入"
                        }
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # 创建配置记录
                config = DLTrainingConfig.objects.create(
                    config_name=config_data['configName'],
                    description=config_data.get('description', ''),
                    algorithm_config=config_data['algorithm'],
                    training_config=config_data['training'],
                    resources_config=config_data.get('resources', {}),
                    parameters_config=config_data.get('parameters', {}),
                    other_params_config=config_data.get('otherParams', {}),
                    created_by=request.user
                )
                
                logger.info(f"Successfully imported config: {config.config_id}")
                return Response({
                    "success": True,
                    "data": {
                        "configId": config.config_id,
                        "configName": config.config_name,
                        "message": "配置导入成功"
                    }
                }, status=status.HTTP_201_CREATED)
                
            except json.JSONDecodeError:
                return Response({
                    "success": False,
                    "error": {
                        "code": "INVALID_JSON",
                        "message": "配置文件格式错误，请确保是有效的JSON格式"
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            except UnicodeDecodeError:
                return Response({
                    "success": False,
                    "error": {
                        "code": "INVALID_ENCODING",
                        "message": "配置文件编码错误，请使用UTF-8编码"
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            logger.error(f"Failed to import DL config: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DLTrainingConfigListView(APIView):
    """获取深度学习配置列表"""
    
    def get(self, request):
        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('pageSize', 20))
            search = request.GET.get('search', '')
            
            # 构建查询
            queryset = DLTrainingConfig.objects.filter(created_by=request.user)
            
            if search:
                queryset = queryset.filter(
                    Q(config_name__icontains=search) |
                    Q(description__icontains=search)
                )
            
            # 分页
            paginator = Paginator(queryset.order_by('-created_time'), page_size)
            page_obj = paginator.get_page(page)
            
            # 序列化数据
            serializer = DLTrainingConfigSerializer(page_obj.object_list, many=True)
            
            return Response({
                "success": True,
                "data": serializer.data,
                "total": paginator.count,
                "page": page,
                "pageSize": page_size,
                "totalPages": paginator.num_pages
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to get DL config list: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DLTrainingConfigDeleteView(APIView):
    """删除深度学习训练配置"""
    
    def delete(self, request, config_id):
        try:
            # 查找配置
            try:
                config = DLTrainingConfig.objects.get(
                    config_id=config_id,
                    created_by=request.user
                )
            except DLTrainingConfig.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "CONFIG_NOT_FOUND",
                        "message": "配置不存在或您没有权限删除"
                    }
                }, status=status.HTTP_404_NOT_FOUND)
            
            config_name = config.config_name
            config.delete()
            
            logger.info(f"Successfully deleted DL config: {config_id}")
            return Response({
                "success": True,
                "message": f"配置 '{config_name}' 已删除"
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to delete DL config: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DLTrainingConfigExportView(APIView):
    """导出深度学习训练配置"""
    
    def get(self, request, config_id):
        try:
            # 查找配置
            try:
                config = DLTrainingConfig.objects.get(
                    config_id=config_id,
                    created_by=request.user
                )
            except DLTrainingConfig.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "CONFIG_NOT_FOUND",
                        "message": "配置不存在或您没有权限导出"
                    }
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 构建导出数据
            export_data = {
                "configName": config.config_name,
                "description": config.description,
                "algorithm": config.algorithm_config,
                "training": config.training_config,
                "resources": config.resources_config,
                "parameters": config.parameters_config,
                "otherParams": config.other_params_config,
                "exportTime": timezone.now().isoformat(),
                "version": "1.0"
            }
            
            from django.http import JsonResponse
            response = JsonResponse(export_data, json_dumps_params={'ensure_ascii': False, 'indent': 2})
            response['Content-Disposition'] = f'attachment; filename="{config.config_name}_config.json"'
            return response
            
        except Exception as e:
            logger.error(f"Failed to export DL config: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingLogsView(APIView):
    """获取训练日志的视图"""

    def get(self, request, training_id):
        """
        获取训练日志
        参数:
            lines: 获取的行数 (默认: 100)
            mode: 获取模式 ('tail': 最后几行, 'head': 前几行, 'all': 全部)
            search: 搜索关键词 (可选)
        """
        try:
            training_id = int(training_id)
            training_task = get_object_or_404(TrainingTask, id=training_id)

            # 获取参数
            lines = int(request.query_params.get('lines', 100))
            mode = request.query_params.get('mode', 'tail')
            search_pattern = request.query_params.get('search', '')

            # 获取训练器实例
            trainer = get_or_create_trainer(training_id)
            if not trainer:
                return Response({
                    'success': False,
                    'message': '训练任务未找到或已结束'
                }, status=status.HTTP_404_NOT_FOUND)

            # 根据是否有搜索关键词选择不同的方法
            if search_pattern:
                logs = trainer.search_logs(search_pattern, lines if mode != 'all' else None)
                log_type = 'search'
            else:
                logs = trainer.get_training_logs(lines, mode)
                log_type = mode

          

            return Response({
                'success': True,
                'data': {
                    'training_id': training_id,
                    'logs': logs,
                    'log_type': log_type,
                    'lines_requested': lines,
                    'search_pattern': search_pattern,
                    'timestamp': time.time()
                }
            }, status=status.HTTP_200_OK)

        except ValueError:
            return Response({
                'success': False,
                'message': '无效的训练任务ID'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"获取训练日志失败: {e}")
            return Response({
                'success': False,
                'message': f'获取日志失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingLogStreamView(View):
    """实时推送训练日志的视图 (Server-Sent Events)"""

    def get(self, request, training_id):
        """GET方式建立SSE连接（token通过URL参数传递）"""
        return self._handle_sse_connection(request, training_id)

    def post(self, request, training_id):
        """POST方式建立SSE连接（token通过请求体传递，适用于长token）"""
        return self._handle_sse_connection(request, training_id)

    def _handle_sse_connection(self, request, training_id):
        """建立SSE连接，实时推送训练日志"""

        # 记录请求信息用于调试
        accept_header = request.META.get('HTTP_ACCEPT', '')
        logger.info(f"日志流SSE请求Accept头: {accept_header}")
        logger.info(f"日志流SSE请求方法: {request.method}")
        logger.info(f"日志流SSE请求路径: {request.path}")

        # 支持通过URL参数传递token（用于EventSource）
        token = request.GET.get('token')
        if not token:
            logger.error("日志流SSE连接缺少token参数")
            return JsonResponse({
                'error': 'Missing token',
                'detail': 'Token parameter is required for SSE connection'
            }, status=401)

        if token:
            from rest_framework_simplejwt.authentication import JWTAuthentication
            from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
            import urllib.parse

            try:
                # 处理可能的URL编码
                decoded_token = urllib.parse.unquote(token)

                # 去掉可能的 "Bearer " 前缀
                if decoded_token.startswith('Bearer '):
                    decoded_token = decoded_token[7:]

                # 去掉首尾空格
                decoded_token = decoded_token.strip()

                # 验证token长度（JWT token通常很长）
                if len(decoded_token) < 50:
                    logger.error(f"日志流Token长度不足: {len(decoded_token)}, token: {decoded_token[:20]}...")
                    return JsonResponse({
                        'error': 'Invalid token format',
                        'detail': f'Token too short: {len(decoded_token)} characters'
                    }, status=401)

                # 记录token信息用于调试
                logger.info(f"尝试验证日志流token，长度: {len(decoded_token)}, 前20字符: {decoded_token[:20]}...")

                jwt_auth = JWTAuthentication()
                validated_token = jwt_auth.get_validated_token(decoded_token)
                user = jwt_auth.get_user(validated_token)
                request.user = user

                logger.info(f"日志流SSE认证成功，用户: {user.username}, 训练任务: {training_id}")

            except (InvalidToken, TokenError) as e:
                logger.error(f"日志流SSE token验证失败: {str(e)}, token长度: {len(token)}, 处理后长度: {len(decoded_token) if 'decoded_token' in locals() else 'N/A'}")
                return JsonResponse({
                    'error': 'Invalid token',
                    'detail': str(e),
                    'token_length': len(token),
                    'processed_token_length': len(decoded_token) if 'decoded_token' in locals() else None
                }, status=401)
            except Exception as e:
                logger.error(f"日志流SSE认证过程中发生错误: {str(e)}")
                return JsonResponse({
                    'error': 'Authentication error',
                    'detail': str(e)
                }, status=401)

        def log_event_stream():
            """SSE日志事件流生成器"""
            try:
                training_task = get_object_or_404(TrainingTask, id=training_id)
                trainer = get_or_create_trainer(training_id)

                last_log_hash = None
                heartbeat_counter = 0
                final_sent = False  # 标记是否已发送最终数据

                logger.info(f"开始SSE推送训练日志，任务ID: {training_id}")

                # 发送初始连接确认
                yield f"event: connected\ndata: {json.dumps({'message': 'Connected to training log stream', 'training_id': training_id, 'timestamp': int(time.time())})}\n\n"

                while True:
                    try:
                        # 重新获取训练任务状态（可能在其他地方被更新）
                        training_task.refresh_from_db()

                        # 检查训练器是否存在
                        if not trainer:
                            trainer = get_or_create_trainer(training_id)
                            if not trainer:
                                yield f"event: error\ndata: {json.dumps({'error': 'Training task not found or not active', 'training_id': training_id, 'timestamp': int(time.time())})}\n\n"
                                time.sleep(10)
                                continue

                        # 获取当前日志
                        try:
                            current_logs = trainer.get_training_logs(lines=50, mode='tail')
                            if current_logs and current_logs != "暂无日志":
                                # 计算日志内容的哈希值，避免重复发送相同内容
                                import hashlib
                                current_hash = hashlib.md5(current_logs.encode()).hexdigest()

                                if current_hash != last_log_hash:
                                    yield f"event: logs\ndata: {json.dumps({'type': 'update', 'logs': current_logs, 'timestamp': int(time.time()), 'training_id': training_id})}\n\n"
                                    last_log_hash = current_hash
                        except Exception as log_error:
                            logger.error(f"获取训练日志失败: {log_error}")
                            yield f"event: error\ndata: {json.dumps({'error': f'Failed to get logs: {str(log_error)}', 'timestamp': int(time.time())})}\n\n"

                        # 检查训练状态
                        if training_task.status in ['completed', 'failed', 'cancelled'] and not final_sent:
                            try:
                                # 发送最终日志
                                final_logs = trainer.get_training_logs(lines=100, mode='tail') if trainer else "训练已结束，无法获取更多日志"
                                yield f"event: final\ndata: {json.dumps({'type': 'final', 'status': training_task.status, 'logs': final_logs, 'timestamp': int(time.time()), 'training_id': training_id})}\n\n"
                                final_sent = True
                                logger.info(f"训练任务 {training_id} 已结束，状态: {training_task.status}")
                                break
                            except Exception as final_error:
                                logger.error(f"发送最终日志失败: {final_error}")
                                yield f"event: error\ndata: {json.dumps({'error': f'Failed to send final logs: {str(final_error)}', 'timestamp': int(time.time())})}\n\n"

                        # 发送心跳（每30秒）
                        heartbeat_counter += 1
                        if heartbeat_counter >= 15:  # 15 * 2秒 = 30秒
                            yield f"event: heartbeat\ndata: {json.dumps({'message': 'heartbeat', 'timestamp': int(time.time()), 'training_id': training_id})}\n\n"
                            heartbeat_counter = 0

                        time.sleep(2)  # 每2秒检查一次

                    except Exception as inner_e:
                        logger.error(f"日志流SSE推送过程中发生错误: {inner_e}")
                        yield f"event: error\ndata: {json.dumps({'error': str(inner_e), 'timestamp': int(time.time())})}\n\n"
                        time.sleep(10)  # 错误后等待更长时间

            except Exception as e:
                logger.error(f"日志流SSE连接初始化失败: {e}")
                yield f"event: error\ndata: {json.dumps({'error': f'Connection failed: {str(e)}', 'timestamp': int(time.time())})}\n\n"

        # 创建SSE响应
        response = StreamingHttpResponse(
            log_event_stream(),
            content_type='text/event-stream'
        )

        # 设置SSE必要的响应头
        response['Cache-Control'] = 'no-cache'
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Headers'] = 'Cache-Control'

        return response