import uuid
from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()

class Training(models.Model):
    """
    训练任务模型
    包含训练特有的配置信息
    """
    task = models.OneToOneField(
        'backend_api.Task',
        on_delete=models.CASCADE,
        related_name='training',
        verbose_name='关联任务',
        null=True
    )
    
    # 资源配置
    actor_num = models.IntegerField('actor数量', default=0, blank=True)
    learner_num = models.IntegerField('learner数量', default=0, blank=True)   # 1
    actor_per_cpu = models.FloatField('单个actor cpu数量', default=0, blank=True)   # 1~2 core
    actor_per_gpu = models.FloatField('单个actor gpu数量', default=0, blank=True)    # 0
    actor_per_memory = models.FloatField('单个actor 内存数量', default=0, blank=True)  # 256 Mi
    learner_per_cpu = models.FloatField('单个learner cpu数量', default=0, blank=True)  # 10 core
    learner_per_gpu = models.FloatField('单个learner gpu数量', default=0, blank=True) # 》 1
    learner_per_memory = models.FloatField('单个learner 内存数量', default=0, blank=True) # 256 Mi
    
    entrypoint = models.CharField('入口函数', max_length=255, null=True, blank=True)
    mount_path = models.CharField('挂载路径', max_length=255, null=True, blank=True)
    tb_url = models.CharField('Tensorboard地址', max_length=255, null=True, blank=True)
    
    model = models.ForeignKey(
        'backend_api.Model',
        on_delete=models.SET_NULL,
        verbose_name='模型',
        related_name='trainings',
        db_constraint=False,
        null=True,
        blank=True
    )
    
    algorithm = models.ForeignKey(
        'backend_api.Algorithm',
        on_delete=models.SET_NULL,
        verbose_name='训练算法',
        related_name='trainings',
        db_constraint=False,
        null=True,
        blank=True
    )
    
    class Meta:
        verbose_name = '训练配置'
        verbose_name_plural = verbose_name
        ordering = ('-id',)

class TrainingTask(models.Model):
    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('running', '运行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    # 基本信息
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # 算法配置
    algorithm_version = models.CharField('算法版本', max_length=50, null=True, blank=True, default='v8')
    model_path = models.CharField('模型路径', max_length=255)  # YOLOv8必需
    
    # 训练数据集配置
    dataset_id = models.IntegerField('数据集ID')  # YOLOv8必需
    dataset_name = models.CharField('数据集名称', max_length=100)  # YOLOv8必需
    validation_ratio = models.FloatField('验证集比例', default=0.2)  # YOLOv8必需
    
    # 资源配置 - 全部设置默认值，避免数据库空值错误
    cpu_count = models.CharField('CPU数量', max_length=10, default='0')
    npu_count = models.CharField('NPU数量', max_length=10, default='0')
    storage_size = models.CharField('存储大小', max_length=20, default='0')
    
    # 训练参数
    learning_rate = models.CharField('学习率', max_length=20, default='0.01')  # YOLOv8必需
    epochs = models.CharField('训练轮数', max_length=10, default='100')  # YOLOv8必需
    max_grad_norm = models.CharField('梯度范数', max_length=20, default='0')
    max_samples = models.CharField('最大样本数', max_length=20, default='0')
    grad_accumulation = models.CharField('梯度累计', max_length=20, default='1')
    batch_size = models.CharField('批处理大小', max_length=20, default='16')  # YOLOv8必需
    learning_rate_strategy = models.CharField('学习率策略', max_length=50, default='')
    compute_type = models.CharField('计算类型', max_length=50, default='float32')
    
    # 其他参数
    optimizer = models.CharField('优化器', max_length=50, default='SGD')  # YOLOv8必需
    momentum = models.CharField('动量', max_length=20, default='0.937')  # YOLOv8必需
    weight_decay = models.CharField('权重衰减', max_length=20, default='0.0005')  # YOLOv8必需
    epsilon = models.CharField('epsilon', max_length=20, default='1e-8')
    dropout = models.CharField('dropout', max_length=20, default='0')
    label_smoothing = models.CharField('标签平滑', max_length=20, default='0')
    use_gradient_clipping = models.BooleanField('使用梯度裁剪', default=False)
    use_mixed_precision = models.BooleanField('使用混合精度', default=False)
    early_stopping = models.IntegerField('早停轮数', default=0)
    checkpoint_freq = models.IntegerField('检查点频率', default=0)
    warmup_steps = models.IntegerField('预热步数', default=0)
    log_freq = models.IntegerField('日志频率', default=100)
    activation = models.CharField('激活函数', max_length=50, default='')
    initialization = models.CharField('初始化方法', max_length=50, default='')
    normalization = models.CharField('归一化方法', max_length=50, default='')
    attention_heads = models.IntegerField('注意力头数', default=0)

    # 服务器信息（用于训练器恢复和远程访问）
    server_ip = models.CharField('服务器IP', max_length=255, blank=True, null=True)
    server_port = models.CharField('服务器端口', max_length=10, blank=True, null=True)
    server_password = models.CharField('服务器密码', max_length=255, blank=True, null=True)

    class Meta:
        verbose_name = '训练任务'
        verbose_name_plural = verbose_name




class DLTrainingConfig(models.Model):
    """深度学习训练配置模型"""
    
    config_id = models.CharField('配置ID', max_length=100, unique=True, default=uuid.uuid4)
    config_name = models.CharField('配置名称', max_length=100)
    description = models.TextField('描述', blank=True)
    
    # 配置JSON数据
    algorithm_config = models.JSONField('算法配置')
    training_config = models.JSONField('训练配置')
    resources_config = models.JSONField('资源配置')
    parameters_config = models.JSONField('参数配置')
    other_params_config = models.JSONField('其他参数配置')
    
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    created_time = models.DateTimeField('创建时间', auto_now_add=True)
    updated_time = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '深度学习训练配置'
        verbose_name_plural = verbose_name
        ordering = ['-created_time']